#!/usr/bin/env python3
"""
🔍 CHECK TRAINING STATUS
Controlla rapidamente lo stato del training
"""

import subprocess
import os
from datetime import datetime

def check_processes():
    """Controlla i processi di training"""
    try:
        result = subprocess.run(['pgrep', '-f', 'train_lora_multi_gpu_simple'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            return [pid for pid in pids if pid]
        return []
    except:
        return []

def check_gpu_usage():
    """Controlla utilizzo GPU"""
    try:
        result = subprocess.run([
            'nvidia-smi', '--query-gpu=index,memory.used,memory.total,utilization.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            active_gpus = []
            for line in lines:
                parts = line.split(', ')
                if len(parts) >= 4:
                    gpu_idx = parts[0]
                    mem_used = int(parts[1])
                    mem_total = int(parts[2])
                    gpu_util = int(parts[3])
                    if mem_used > 100:  # GPU con memoria utilizzata
                        active_gpus.append(f"GPU{gpu_idx}: {mem_used}/{mem_total}MB ({gpu_util}%)")
            return active_gpus
        return []
    except:
        return []

def check_latest_checkpoints():
    """Controlla gli ultimi checkpoint"""
    models = {
        "Gemma-T9": "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized",
        "Llama-T8": "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
    }
    
    results = {}
    for model_name, model_dir in models.items():
        try:
            if os.path.exists(model_dir):
                checkpoints = []
                for item in os.listdir(model_dir):
                    if item.startswith('checkpoint-'):
                        try:
                            step = int(item.split('-')[1])
                            checkpoint_path = os.path.join(model_dir, item)
                            mtime = os.path.getmtime(checkpoint_path)
                            checkpoints.append((step, mtime, item))
                        except:
                            continue
                
                if checkpoints:
                    checkpoints.sort(key=lambda x: x[0])
                    latest = checkpoints[-1]
                    latest_time = datetime.fromtimestamp(latest[1]).strftime('%H:%M:%S')
                    results[model_name] = f"{latest[2]} ({latest_time})"
                else:
                    results[model_name] = "Nessun checkpoint"
            else:
                results[model_name] = "Directory non trovata"
        except:
            results[model_name] = "Errore"
    
    return results

def main():
    print("🔍 TRAINING STATUS CHECK")
    print("=" * 50)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Controlla processi
    pids = check_processes()
    print(f"🔸 Processi attivi: {len(pids)}")
    for i, pid in enumerate(pids, 1):
        model_name = "Gemma-T9" if i == 1 else "Llama-T8"
        print(f"   {model_name}: PID {pid}")
    
    if not pids:
        print("   ❌ Nessun processo di training attivo!")
        return
    
    print()
    
    # Controlla GPU
    active_gpus = check_gpu_usage()
    print(f"🖥️  GPU attive: {len(active_gpus)}")
    if active_gpus:
        for gpu in active_gpus:
            print(f"   {gpu}")
    else:
        print("   ⚠️ Nessuna GPU in uso (modelli potrebbero essere in caricamento)")
    
    print()
    
    # Controlla checkpoint
    checkpoints = check_latest_checkpoints()
    print("📂 Ultimi checkpoint:")
    for model_name, checkpoint_info in checkpoints.items():
        print(f"   {model_name}: {checkpoint_info}")
    
    print()
    print("💡 Usa 'python check_training_status.py' per aggiornare lo status")

if __name__ == "__main__":
    main()
