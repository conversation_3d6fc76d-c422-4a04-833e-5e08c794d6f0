#!/usr/bin/env python3
"""
🚀 RESTART DAL CHECKPOINT CORRETTO
Riprende il training dal checkpoint giusto
"""

import os
import sys
import subprocess
import logging
import json
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_trainer_state(checkpoint_dir, step):
    """Crea un trainer_state.json compatibile"""
    trainer_state = {
        "best_metric": None,
        "best_model_checkpoint": None,
        "epoch": step / 16875,  # Approssimazione
        "global_step": step,
        "is_hyper_param_search": False,
        "is_local_process_zero": True,
        "is_world_process_zero": True,
        "log_history": [],
        "max_steps": 16875,
        "num_train_epochs": 1,
        "total_flos": 0,
        "trial_name": None,
        "trial_params": None
    }
    
    trainer_state_path = os.path.join(checkpoint_dir, "trainer_state.json")
    with open(trainer_state_path, "w") as f:
        json.dump(trainer_state, f, indent=2)
    
    logger.info(f"✅ Creato trainer_state.json per step {step}")
    return trainer_state_path

def restart_gemma_from_checkpoint():
    """Riavvia Gemma dal checkpoint 6000"""
    logger.info("🚀 RIAVVIO GEMMA DAL CHECKPOINT 6000")
    
    checkpoint_dir = "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/checkpoint-6000"
    
    # Crea trainer_state.json
    create_trainer_state(checkpoint_dir, 6000)
    
    # Cancella job attuale
    subprocess.run(["scancel", "2609951"], capture_output=True)
    
    # Crea script SLURM
    slurm_script = f"""#!/bin/bash
#SBATCH --job-name=GEMMA_FROM_6000
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/GEMMA_FROM_6000_%j.out
#SBATCH --error=logs/GEMMA_FROM_6000_%j.err

echo "🚀 GEMMA FROM CHECKPOINT 6000 - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name google/gemma-2-9b-it \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json \\
    --output_dir experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized \\
    --resume_from_checkpoint {checkpoint_dir} \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name gemma_from_6000_$(date +%Y%m%d_%H%M%S)

echo "✅ GEMMA FROM 6000 completato - $(date)"
"""
    
    with open("restart_gemma_from_6000.slurm", "w") as f:
        f.write(slurm_script)
    
    # Lancia job
    result = subprocess.run(["sbatch", "restart_gemma_from_6000.slurm"], capture_output=True, text=True)
    if result.returncode == 0:
        job_id = result.stdout.strip().split()[-1]
        logger.info(f"✅ Gemma from 6000 job lanciato: {job_id}")
        return job_id
    else:
        logger.error(f"❌ Errore: {result.stderr}")
        return None

def restart_llama_from_checkpoint():
    """Riavvia Llama dal checkpoint 8000"""
    logger.info("🚀 RIAVVIO LLAMA DAL CHECKPOINT 8000")
    
    checkpoint_dir = "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/checkpoint-8000"
    
    # Crea trainer_state.json
    create_trainer_state(checkpoint_dir, 8000)
    
    # Cancella job attuale
    subprocess.run(["scancel", "2609952"], capture_output=True)
    
    # Crea script SLURM
    slurm_script = f"""#!/bin/bash
#SBATCH --job-name=LLAMA_FROM_8000
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/LLAMA_FROM_8000_%j.out
#SBATCH --error=logs/LLAMA_FROM_8000_%j.err

echo "🚀 LLAMA FROM CHECKPOINT 8000 - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name meta-llama/Llama-3.1-8B-Instruct \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \\
    --output_dir experiments/xml_direct_input/outputs/llama_t8_scratch_quantized \\
    --resume_from_checkpoint {checkpoint_dir} \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name llama_from_8000_$(date +%Y%m%d_%H%M%S)

echo "✅ LLAMA FROM 8000 completato - $(date)"
"""
    
    with open("restart_llama_from_8000.slurm", "w") as f:
        f.write(slurm_script)
    
    # Lancia job
    result = subprocess.run(["sbatch", "restart_llama_from_8000.slurm"], capture_output=True, text=True)
    if result.returncode == 0:
        job_id = result.stdout.strip().split()[-1]
        logger.info(f"✅ Llama from 8000 job lanciato: {job_id}")
        return job_id
    else:
        logger.error(f"❌ Errore: {result.stderr}")
        return None

def main():
    logger.info("🚀 RESTART DAI CHECKPOINT CORRETTI")
    logger.info("=" * 50)
    
    # Riavvia entrambi dai checkpoint corretti
    gemma_job = restart_gemma_from_checkpoint()
    llama_job = restart_llama_from_checkpoint()
    
    logger.info("💡 Controlla i job con: squeue -u ediluzio")
    logger.info("💡 Ora i modelli riprenderanno dai checkpoint corretti!")

if __name__ == "__main__":
    main()
