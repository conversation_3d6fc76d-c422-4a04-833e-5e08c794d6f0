#!/bin/bash
#SBATCH --job-name=LLAMA_MULTI_GPU
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --output=logs/LLAMA_MULTI_GPU_%j.out
#SBATCH --error=logs/LLAMA_MULTI_GPU_%j.err

echo "🚀 LLAMA MULTI-GPU - $(date)"
echo "Checkpoint: experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/checkpoint-8000"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

# Multi-GPU training con torchrun
torchrun --nproc_per_node=2 --master_port=29501 \
    scripts/training/train_lora_ULTRA_QUANTIZED.py \
    --model_name meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t8_scratch_quantized \
    --use_wandb \
    --wandb_project svg-captioning-quantized \
    --wandb_run_name llama_multi_gpu_$(date +%Y%m%d_%H%M%S)

echo "✅ LLAMA MULTI-GPU completato - $(date)"
