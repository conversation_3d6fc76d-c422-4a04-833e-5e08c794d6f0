#!/bin/bash
#SBATCH --job-name=RESTART_QUANTIZED
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=32G
#SBATCH --time=12:00:00
#SBATCH --output=restart_quantized_%j.out
#SBATCH --error=restart_quantized_%j.err

echo "🚀 RIAVVIO MODELLI QUANTIZZATI CON SLURM"
echo "========================================"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: $CUDA_VISIBLE_DEVICES"
echo "Time: $(date)"
echo ""

# Setup environment
module load cuda/11.8
module load python/3.9
source /homes/ediluzio/.bashrc

# Activate conda environment if needed
# conda activate your_env_name

cd /work/tesi_ediluzio

echo "🔧 Environment setup complete"
echo "Python: $(which python)"
echo "CUDA: $(nvcc --version | grep release)"
echo ""

# Function to start training
start_training() {
    local model_name=$1
    local model_path=$2
    local output_dir=$3
    local checkpoint_dir=$4
    local log_file=$5
    
    echo "🚀 Starting $model_name training..."
    echo "   Model: $model_path"
    echo "   Output: $output_dir"
    echo "   Checkpoint: $checkpoint_dir"
    echo "   Log: $log_file"
    
    python scripts/training/train_lora_multi_gpu_simple.py \
        --model_name_or_path "$model_path" \
        --data_file "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json" \
        --output_dir "$output_dir" \
        --resume_from_checkpoint "$checkpoint_dir" \
        --num_train_epochs 5 \
        --per_device_train_batch_size 1 \
        --gradient_accumulation_steps 16 \
        --learning_rate 2e-4 \
        --logging_steps 50 \
        --save_steps 500 \
        --max_seq_length 2048 \
        --use_wandb \
        --wandb_project "svg-captioning-quantized" \
        --wandb_run_name "${model_name}_resume_$(date +%Y%m%d_%H%M%S)" \
        > "$log_file" 2>&1 &
    
    local pid=$!
    echo "   ✅ Started with PID: $pid"
    return $pid
}

# Start Gemma-T9
echo "🔸 GEMMA-T9 QUANTIZZATO"
echo "------------------------"
GEMMA_OUTPUT="experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
GEMMA_CHECKPOINT="$GEMMA_OUTPUT/checkpoint-6000"
GEMMA_LOG="$GEMMA_OUTPUT/slurm_training.log"

if [ -d "$GEMMA_CHECKPOINT" ]; then
    start_training "gemma_t9" "google/gemma-2-9b-it" "$GEMMA_OUTPUT" "$GEMMA_CHECKPOINT" "$GEMMA_LOG"
    GEMMA_PID=$!
    echo "   📝 Log: $GEMMA_LOG"
else
    echo "   ❌ Checkpoint not found: $GEMMA_CHECKPOINT"
    GEMMA_PID=0
fi

echo ""

# Start Llama-T8
echo "🔸 LLAMA-T8 QUANTIZZATO"
echo "------------------------"
LLAMA_OUTPUT="experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
LLAMA_CHECKPOINT="$LLAMA_OUTPUT/checkpoint-8000"
LLAMA_LOG="$LLAMA_OUTPUT/slurm_training.log"

if [ -d "$LLAMA_CHECKPOINT" ]; then
    start_training "llama_t8" "meta-llama/Llama-3.1-8B-Instruct" "$LLAMA_OUTPUT" "$LLAMA_CHECKPOINT" "$LLAMA_LOG"
    LLAMA_PID=$!
    echo "   📝 Log: $LLAMA_LOG"
else
    echo "   ❌ Checkpoint not found: $LLAMA_CHECKPOINT"
    LLAMA_PID=0
fi

echo ""
echo "🎯 TRAINING STARTED"
echo "==================="
echo "Gemma-T9 PID: $GEMMA_PID"
echo "Llama-T8 PID: $LLAMA_PID"
echo ""

# Monitor for a bit
echo "📊 Initial monitoring (60 seconds)..."
for i in {1..12}; do
    echo "Check $i/12 - $(date +%H:%M:%S)"
    
    # Check if processes are still running
    if [ $GEMMA_PID -ne 0 ]; then
        if kill -0 $GEMMA_PID 2>/dev/null; then
            echo "   🟢 Gemma-T9 running"
        else
            echo "   🔴 Gemma-T9 stopped"
        fi
    fi
    
    if [ $LLAMA_PID -ne 0 ]; then
        if kill -0 $LLAMA_PID 2>/dev/null; then
            echo "   🟢 Llama-T8 running"
        else
            echo "   🔴 Llama-T8 stopped"
        fi
    fi
    
    # Show GPU usage
    nvidia-smi --query-gpu=index,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits | grep -v "0, 0" | head -3
    
    sleep 5
done

echo ""
echo "✅ INITIAL MONITORING COMPLETE"
echo "Training processes should continue running in background"
echo "Check logs:"
echo "  - $GEMMA_LOG"
echo "  - $LLAMA_LOG"
echo ""
echo "Monitor with: squeue -u $USER"
echo "Job will run for up to 12 hours"

# Keep job alive to monitor training
wait
