#!/usr/bin/env python3
"""
🚀 RESTART SENZA RESUME
Riavvia i modelli senza usare resume_from_checkpoint
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def restart_with_slurm():
    """Riavvia con SLURM senza resume"""
    logger.info("🚀 RIAVVIO SENZA RESUME")
    
    # Crea script SLURM per Gemma
    slurm_script = f"""#!/bin/bash
#SBATCH --job-name=GEMMA_NO_RESUME
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/GEMMA_NO_RESUME_%j.out
#SBATCH --error=logs/GEMMA_NO_RESUME_%j.err

echo "🚀 GEMMA NO RESUME - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name google/gemma-2-9b-it \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json \\
    --output_dir experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name gemma_no_resume_$(date +%Y%m%d_%H%M%S)

echo "✅ GEMMA NO RESUME completato - $(date)"
"""
    
    with open("restart_gemma_no_resume.slurm", "w") as f:
        f.write(slurm_script)
    
    # Lancia job Gemma
    result = subprocess.run(["sbatch", "restart_gemma_no_resume.slurm"], capture_output=True, text=True)
    if result.returncode == 0:
        job_id = result.stdout.strip().split()[-1]
        logger.info(f"✅ Gemma job lanciato: {job_id}")
    else:
        logger.error(f"❌ Errore Gemma: {result.stderr}")

    # Crea script SLURM per Llama
    slurm_script = f"""#!/bin/bash
#SBATCH --job-name=LLAMA_NO_RESUME
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/LLAMA_NO_RESUME_%j.out
#SBATCH --error=logs/LLAMA_NO_RESUME_%j.err

echo "🚀 LLAMA NO RESUME - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name meta-llama/Llama-3.1-8B-Instruct \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \\
    --output_dir experiments/xml_direct_input/outputs/llama_t8_scratch_quantized \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name llama_no_resume_$(date +%Y%m%d_%H%M%S)

echo "✅ LLAMA NO RESUME completato - $(date)"
"""
    
    with open("restart_llama_no_resume.slurm", "w") as f:
        f.write(slurm_script)
    
    # Lancia job Llama
    result = subprocess.run(["sbatch", "restart_llama_no_resume.slurm"], capture_output=True, text=True)
    if result.returncode == 0:
        job_id = result.stdout.strip().split()[-1]
        logger.info(f"✅ Llama job lanciato: {job_id}")
    else:
        logger.error(f"❌ Errore Llama: {result.stderr}")

def main():
    logger.info("🚀 RESTART SENZA RESUME")
    logger.info("=" * 50)
    
    restart_with_slurm()
    
    logger.info("💡 Controlla i job con: squeue -u ediluzio")
    logger.info("💡 Controlla i log in: logs/")

if __name__ == "__main__":
    main()
