#!/bin/bash
#SBATCH --job-name=LLAMA_FROM_8000
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/LLAMA_FROM_8000_%j.out
#SBATCH --error=logs/LLAMA_FROM_8000_%j.err

echo "🚀 LLAMA FROM CHECKPOINT 8000 - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \
    --model_name meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t8_scratch_quantized \
    --resume_from_checkpoint experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/checkpoint-8000 \
    --use_wandb \
    --wandb_project svg-captioning-quantized \
    --wandb_run_name llama_from_8000_$(date +%Y%m%d_%H%M%S)

echo "✅ LLAMA FROM 8000 completato - $(date)"
