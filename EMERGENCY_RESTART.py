#!/usr/bin/env python3
"""
🚨 EMERGENCY RESTART - FUNZIONA SICURAMENTE
"""

import subprocess
import os

def emergency_restart():
    print("🚨 EMERGENCY RESTART - RIAVVIO IMMEDIATO")
    
    # Script SLURM per Gemma
    gemma_script = """#!/bin/bash
#SBATCH --job-name=EMERGENCY_GEMMA
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/EMERGENCY_GEMMA_%j.out
#SBATCH --error=logs/EMERGENCY_GEMMA_%j.err

echo "🚨 EMERGENCY GEMMA - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_multi_gpu_simple.py \\
    --model_name_or_path google/gemma-2-9b-it \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json \\
    --output_dir experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized \\
    --resume_from_checkpoint experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/checkpoint-6000 \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name emergency_gemma_$(date +%Y%m%d_%H%M%S)

echo "✅ EMERGENCY GEMMA completato - $(date)"
"""
    
    # Script SLURM per Llama
    llama_script = """#!/bin/bash
#SBATCH --job-name=EMERGENCY_LLAMA
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/EMERGENCY_LLAMA_%j.out
#SBATCH --error=logs/EMERGENCY_LLAMA_%j.err

echo "🚨 EMERGENCY LLAMA - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_multi_gpu_simple.py \\
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \\
    --output_dir experiments/xml_direct_input/outputs/llama_t8_scratch_quantized \\
    --resume_from_checkpoint experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/checkpoint-8000 \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name emergency_llama_$(date +%Y%m%d_%H%M%S)

echo "✅ EMERGENCY LLAMA completato - $(date)"
"""
    
    # Scrivi script
    with open("emergency_gemma.slurm", "w") as f:
        f.write(gemma_script)
    
    with open("emergency_llama.slurm", "w") as f:
        f.write(llama_script)
    
    # Lancia job
    result1 = subprocess.run(["sbatch", "emergency_gemma.slurm"], capture_output=True, text=True)
    result2 = subprocess.run(["sbatch", "emergency_llama.slurm"], capture_output=True, text=True)
    
    if result1.returncode == 0:
        job1 = result1.stdout.strip().split()[-1]
        print(f"✅ GEMMA EMERGENCY: {job1}")
    else:
        print(f"❌ GEMMA ERROR: {result1.stderr}")
    
    if result2.returncode == 0:
        job2 = result2.stdout.strip().split()[-1]
        print(f"✅ LLAMA EMERGENCY: {job2}")
    else:
        print(f"❌ LLAMA ERROR: {result2.stderr}")

if __name__ == "__main__":
    emergency_restart()
