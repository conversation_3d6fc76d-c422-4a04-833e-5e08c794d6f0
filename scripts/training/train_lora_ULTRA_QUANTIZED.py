#!/usr/bin/env python3
import os, sys, json, argparse, logging, torch, gc
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, TrainingArguments, Trainer, BitsAndBytesConfig, DataCollatorForLanguageModeling
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
import wandb

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SVGDataset(torch.utils.data.Dataset):
    def __init__(self, data, tokenizer, max_length=512):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        # Fix: usa 'xml' invece di 'svg_content'
        prompt = f"<svg>{item['xml']}</svg>"
        response = item['caption']
        full_text = f"{prompt} {response}"
        
        encoding = self.tokenizer(
            full_text,
            truncation=True,
            padding=False,
            max_length=self.max_length,
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].flatten(),
            "attention_mask": encoding["attention_mask"].flatten(),
            "labels": encoding["input_ids"].flatten()
        }

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_name", type=str, required=True)
    parser.add_argument("--data_file", type=str, required=True)
    parser.add_argument("--config_path", type=str, required=True)
    parser.add_argument("--output_dir", type=str, required=True)
    parser.add_argument("--use_wandb", action="store_true")
    parser.add_argument("--wandb_project", type=str, default="svg_captioning")
    parser.add_argument("--wandb_run_name", type=str)
    parser.add_argument("--resume_from_checkpoint", type=str, default=None)
    args = parser.parse_args()

    # ULTRA AGGRESSIVE MEMORY OPTIMIZATION
    torch.cuda.empty_cache()
    gc.collect()
    
    # Set environment variables for memory optimization
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    
    logger.info("🔥 ULTRA QUANTIZZAZIONE 8-BIT + OTTIMIZZAZIONI AGGRESSIVE")
    
    # Load config
    with open(args.config_path, 'r') as f:
        config = json.load(f)
    
    # Load tokenizer
    logger.info(f"Caricamento tokenizer: {args.model_name}")
    tokenizer = AutoTokenizer.from_pretrained(args.model_name, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # QUANTIZZAZIONE 4-BIT CON TRANSFORMERS 4.41.0
    logger.info("🚀 QUANTIZZAZIONE 4-BIT CON TRANSFORMERS 4.41.0")
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_use_double_quant=True
    )

    logger.info(f"Caricamento modello QUANTIZZATO: {args.model_name}")
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name,
        quantization_config=quantization_config,
        torch_dtype=torch.float16,
        trust_remote_code=True,
        low_cpu_mem_usage=False,  # Disabilita per evitare dispatch automatico
        device_map=None  # Nessun device mapping automatico
    )

    # Sposta manualmente su GPU dopo il caricamento
    if torch.cuda.is_available():
        logger.info("🔧 Spostamento manuale su GPU...")
        # Non usare .to() direttamente, lascia che sia il trainer a gestirlo

    # Clear cache after model loading
    torch.cuda.empty_cache()
    gc.collect()

    # Prepare model with memory optimization (CON QUANTIZZAZIONE)
    logger.info("🔧 Preparazione modello quantizzato...")
    model = prepare_model_for_kbit_training(model, use_gradient_checkpointing=True)
    
    # ULTRA AGGRESSIVE LORA CONFIG
    lora_config = LoraConfig(
        r=8,  # Ridotto da 16 a 8
        lora_alpha=16,  # Ridotto da 32 a 16
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
        lora_dropout=0.05,  # Ridotto da 0.1 a 0.05
        bias="none", 
        task_type=TaskType.CAUSAL_LM
    )
    
    model = get_peft_model(model, lora_config)
    
    # Load data
    logger.info(f"Caricamento dataset: {args.data_file}")
    with open(args.data_file, 'r') as f:
        data = json.load(f)
    
    logger.info(f"Dataset caricato: {len(data)} esempi")
    dataset = SVGDataset(data, tokenizer, max_length=256)  # Ridotto da 512 a 256
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)
    
    # ULTRA AGGRESSIVE TRAINING ARGS
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=config.get("num_train_epochs", 3),
        per_device_train_batch_size=1,  # Batch size minimo
        gradient_accumulation_steps=16,  # Aumentato per compensare batch piccolo
        warmup_steps=config.get("warmup_steps", 100),
        learning_rate=config.get("learning_rate", 2e-4),
        fp16=True,
        logging_steps=config.get("logging_steps", 50),
        save_steps=config.get("save_steps", 500),
        eval_steps=config.get("eval_steps", 500),
        save_total_limit=2,  # Ridotto da 3 a 2
        load_best_model_at_end=False,  # Disabilitato per risparmiare memoria
        report_to="wandb" if args.use_wandb else "none",
        run_name=args.wandb_run_name,
        dataloader_pin_memory=False,  # Disabilita pin memory
        dataloader_num_workers=0,  # Nessun worker parallelo
        gradient_checkpointing=True,  # Abilita gradient checkpointing
        optim="adamw_8bit",  # Optimizer 8-bit
        max_grad_norm=0.3,  # Gradient clipping aggressivo
        remove_unused_columns=False
    )
    
    # Initialize wandb
    if args.use_wandb:
        wandb.init(project=args.wandb_project, name=args.wandb_run_name)
    
    # Trainer
    trainer = Trainer(
        model=model, 
        args=training_args, 
        train_dataset=dataset, 
        data_collator=data_collator, 
        tokenizer=tokenizer
    )
    
    # Clear cache before training
    torch.cuda.empty_cache()
    gc.collect()
    
    logger.info("🚀 Avvio training ULTRA QUANTIZZATO...")
    
    # Resume from checkpoint if specified
    resume_from_checkpoint = args.resume_from_checkpoint if args.resume_from_checkpoint else None
    
    trainer.train(resume_from_checkpoint=resume_from_checkpoint)
    
    # Save final model
    logger.info("💾 Salvataggio modello finale...")
    trainer.save_model()
    tokenizer.save_pretrained(args.output_dir)
    
    logger.info("✅ Training ULTRA QUANTIZZATO completato!")

if __name__ == "__main__":
    main()
