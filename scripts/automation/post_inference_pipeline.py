#!/usr/bin/env python3
"""
Post-Inference Automation Pipeline
Automatizza il calcolo delle metriche e la generazione dei radar charts
una volta completata l'inference
"""

import os
import json
import subprocess
import time
from datetime import datetime
import argparse
import glob

class PostInferencePipeline:
    def __init__(self, project_root="/work/tesi_ediluzio"):
        self.project_root = project_root
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def find_latest_inference_results(self):
        """Trova i risultati di inference più recenti"""
        results_pattern = os.path.join(
            self.project_root, 
            "evaluation_results/production_inference_*/florence2_results_*.json"
        )
        
        florence2_files = glob.glob(results_pattern)
        
        if not florence2_files:
            return None, None
        
        # Prendi il file più recente
        latest_florence2 = max(florence2_files, key=os.path.getmtime)
        
        # Trova il corrispondente BLIP-2
        inference_dir = os.path.dirname(latest_florence2)
        timestamp_part = os.path.basename(latest_florence2).split('_')[-1].replace('.json', '')
        
        blip2_file = os.path.join(inference_dir, f"blip2_results_{timestamp_part}.json")
        
        if os.path.exists(blip2_file):
            return latest_florence2, blip2_file
        else:
            return latest_florence2, None
    
    def validate_results(self, florence2_file, blip2_file):
        """Valida i file di risultati"""
        validation_results = {
            'florence2': {'valid': False, 'count': 0, 'errors': []},
            'blip2': {'valid': False, 'count': 0, 'errors': []}
        }
        
        # Valida Florence-2
        if florence2_file and os.path.exists(florence2_file):
            try:
                with open(florence2_file, 'r') as f:
                    florence2_data = json.load(f)
                
                validation_results['florence2']['count'] = len(florence2_data)
                
                # Controlla che ci siano caption generate
                valid_captions = sum(1 for item in florence2_data 
                                   if item.get('generated_caption', '').strip())
                
                if valid_captions > 0:
                    validation_results['florence2']['valid'] = True
                else:
                    validation_results['florence2']['errors'].append("No valid captions found")
                    
            except Exception as e:
                validation_results['florence2']['errors'].append(f"Error reading file: {e}")
        else:
            validation_results['florence2']['errors'].append("File not found")
        
        # Valida BLIP-2
        if blip2_file and os.path.exists(blip2_file):
            try:
                with open(blip2_file, 'r') as f:
                    blip2_data = json.load(f)
                
                validation_results['blip2']['count'] = len(blip2_data)
                
                # Controlla che ci siano caption generate
                valid_captions = sum(1 for item in blip2_data 
                                   if item.get('generated_caption', '').strip())
                
                if valid_captions > 0:
                    validation_results['blip2']['valid'] = True
                else:
                    validation_results['blip2']['errors'].append("No valid captions found")
                    
            except Exception as e:
                validation_results['blip2']['errors'].append(f"Error reading file: {e}")
        else:
            validation_results['blip2']['errors'].append("File not found")
        
        return validation_results
    
    def calculate_metrics(self):
        """Esegue il calcolo delle metriche reali"""
        print("🔢 Calculating real metrics...")
        
        metrics_script = os.path.join(
            self.project_root, 
            "scripts/evaluation/CALCULATE_ALL_REAL_METRICS.py"
        )
        
        if not os.path.exists(metrics_script):
            print(f"❌ Metrics script not found: {metrics_script}")
            return False
        
        try:
            result = subprocess.run(
                ["python", metrics_script],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                print("✅ Metrics calculation completed successfully")
                return True
            else:
                print(f"❌ Metrics calculation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Metrics calculation timed out")
            return False
        except Exception as e:
            print(f"❌ Error running metrics calculation: {e}")
            return False
    
    def generate_radar_charts(self):
        """Genera i radar charts aggiornati"""
        print("📊 Generating updated radar charts...")
        
        radar_script = os.path.join(
            self.project_root, 
            "scripts/visualization/create_professional_radar_charts.py"
        )
        
        if not os.path.exists(radar_script):
            print(f"❌ Radar charts script not found: {radar_script}")
            return False
        
        try:
            result = subprocess.run(
                ["python", radar_script],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                print("✅ Radar charts generated successfully")
                return True
            else:
                print(f"❌ Radar charts generation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Radar charts generation timed out")
            return False
        except Exception as e:
            print(f"❌ Error generating radar charts: {e}")
            return False
    
    def generate_final_report(self, florence2_file, blip2_file, validation_results):
        """Genera il report finale"""
        print("📋 Generating final report...")
        
        report = {
            'timestamp': self.timestamp,
            'pipeline_completion': datetime.now().isoformat(),
            'inference_results': {
                'florence2_file': florence2_file,
                'blip2_file': blip2_file
            },
            'validation': validation_results,
            'metrics_calculated': False,
            'radar_charts_generated': False
        }
        
        # Calcola metriche
        if self.calculate_metrics():
            report['metrics_calculated'] = True
        
        # Genera radar charts
        if self.generate_radar_charts():
            report['radar_charts_generated'] = True
        
        # Salva report
        report_path = os.path.join(
            self.project_root, 
            f"evaluation_results/pipeline_report_{self.timestamp}.json"
        )
        
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 Final report saved: {report_path}")
        
        return report
    
    def run_pipeline(self):
        """Esegue l'intera pipeline"""
        print("🚀 Starting Post-Inference Pipeline")
        print("=" * 50)
        
        # Trova i risultati più recenti
        print("🔍 Looking for latest inference results...")
        florence2_file, blip2_file = self.find_latest_inference_results()
        
        if not florence2_file:
            print("❌ No inference results found!")
            return False
        
        print(f"📁 Found Florence-2 results: {florence2_file}")
        if blip2_file:
            print(f"📁 Found BLIP-2 results: {blip2_file}")
        else:
            print("⚠️  BLIP-2 results not found")
        
        # Valida i risultati
        print("\n🔍 Validating results...")
        validation_results = self.validate_results(florence2_file, blip2_file)
        
        for model, validation in validation_results.items():
            if validation['valid']:
                print(f"✅ {model.upper()}: {validation['count']} valid captions")
            else:
                print(f"❌ {model.upper()}: Invalid - {', '.join(validation['errors'])}")
        
        # Controlla se almeno un modello ha risultati validi
        valid_models = sum(1 for v in validation_results.values() if v['valid'])
        
        if valid_models == 0:
            print("❌ No valid results found. Cannot proceed.")
            return False
        
        print(f"\n✅ Found {valid_models}/2 valid model results. Proceeding...")
        
        # Genera report finale e esegue pipeline
        report = self.generate_final_report(florence2_file, blip2_file, validation_results)
        
        # Stampa summary
        print("\n" + "=" * 50)
        print("📊 PIPELINE SUMMARY")
        print("=" * 50)
        print(f"✅ Inference Results Found: {valid_models}/2 models")
        print(f"✅ Metrics Calculated: {'Yes' if report['metrics_calculated'] else 'No'}")
        print(f"✅ Radar Charts Generated: {'Yes' if report['radar_charts_generated'] else 'No'}")
        
        if report['metrics_calculated'] and report['radar_charts_generated']:
            print("\n🎉 PIPELINE COMPLETED SUCCESSFULLY!")
            print("🎯 Your thesis now has REAL data for all baseline models!")
        else:
            print("\n⚠️  Pipeline completed with some issues.")
        
        print("=" * 50)
        
        return True

def main():
    parser = argparse.ArgumentParser(description='Post-Inference Automation Pipeline')
    parser.add_argument('--project_root', default='/work/tesi_ediluzio', 
                       help='Project root directory')
    parser.add_argument('--wait_for_completion', action='store_true',
                       help='Wait for inference to complete before running')
    parser.add_argument('--check_interval', type=int, default=300,
                       help='Check interval in seconds when waiting')
    
    args = parser.parse_args()
    
    pipeline = PostInferencePipeline(project_root=args.project_root)
    
    if args.wait_for_completion:
        print("⏳ Waiting for inference to complete...")
        
        while True:
            florence2_file, blip2_file = pipeline.find_latest_inference_results()
            
            if florence2_file:
                # Controlla se i file sono stati modificati di recente
                last_modified = os.path.getmtime(florence2_file)
                time_since_modified = time.time() - last_modified
                
                if time_since_modified > 600:  # 10 minutes since last modification
                    print("✅ Inference appears to be complete. Starting pipeline...")
                    break
            
            print(f"⏳ Still waiting... (checking again in {args.check_interval}s)")
            time.sleep(args.check_interval)
    
    # Esegui pipeline
    success = pipeline.run_pipeline()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
