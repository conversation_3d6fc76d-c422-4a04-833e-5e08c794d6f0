#!/bin/bash
# Watch and Process Script
# Monitora il job SLURM e lancia automaticamente la pipeline post-inference

set -e

# Configuration
JOB_ID=${1:-2609520}
PROJECT_ROOT="/work/tesi_ediluzio"
CHECK_INTERVAL=300  # 5 minutes
MAX_WAIT_TIME=14400  # 4 hours

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if job exists
check_job_exists() {
    if squeue -j "$JOB_ID" &>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Get job status
get_job_status() {
    local status=$(squeue -j "$JOB_ID" --format="%T" --noheader 2>/dev/null | tr -d ' ')
    echo "$status"
}

# Check if job is completed
is_job_completed() {
    if ! check_job_exists; then
        return 0  # Job not found = completed
    fi
    
    local status=$(get_job_status)
    case "$status" in
        "COMPLETED"|"FAILED"|"CANCELLED"|"TIMEOUT")
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Wait for job completion
wait_for_completion() {
    local start_time=$(date +%s)
    local elapsed=0
    
    log "Starting to monitor job $JOB_ID..."
    
    while [ $elapsed -lt $MAX_WAIT_TIME ]; do
        if is_job_completed; then
            success "Job $JOB_ID has completed!"
            return 0
        fi
        
        local status=$(get_job_status)
        local current_time=$(date +%s)
        elapsed=$((current_time - start_time))
        local elapsed_hours=$((elapsed / 3600))
        local elapsed_minutes=$(((elapsed % 3600) / 60))
        
        log "Job status: $status (elapsed: ${elapsed_hours}h ${elapsed_minutes}m)"
        
        # Show some progress info
        if [ "$status" = "RUNNING" ]; then
            # Try to get some progress info from logs
            local log_file="$PROJECT_ROOT/logs/baseline_1gpu_${JOB_ID}.out"
            if [ -f "$log_file" ]; then
                local log_lines=$(wc -l < "$log_file" 2>/dev/null || echo "0")
                local log_size=$(du -h "$log_file" 2>/dev/null | cut -f1 || echo "0")
                log "  Log file: $log_lines lines, $log_size"
                
                # Show last few lines if they contain progress info
                if tail -5 "$log_file" 2>/dev/null | grep -q "Processing\|Completed\|Error"; then
                    log "  Recent activity:"
                    tail -3 "$log_file" 2>/dev/null | sed 's/^/    /' || true
                fi
            fi
        fi
        
        sleep $CHECK_INTERVAL
        elapsed=$(($(date +%s) - start_time))
    done
    
    error "Timeout waiting for job completion after $MAX_WAIT_TIME seconds"
    return 1
}

# Check job final status
check_final_status() {
    log "Checking final job status..."
    
    # Try to get job info from sacct
    local job_info=$(sacct -j "$JOB_ID" --format=State,ExitCode,Elapsed --noheader 2>/dev/null | head -1)
    
    if [ -n "$job_info" ]; then
        log "Final job info: $job_info"
        
        if echo "$job_info" | grep -q "COMPLETED"; then
            success "Job completed successfully!"
            return 0
        else
            warning "Job may have failed or been cancelled"
            return 1
        fi
    else
        warning "Could not retrieve final job status"
        return 1
    fi
}

# Run post-inference pipeline
run_pipeline() {
    log "Starting post-inference pipeline..."
    
    cd "$PROJECT_ROOT"
    
    # Make sure the pipeline script is executable
    chmod +x scripts/automation/post_inference_pipeline.py
    
    # Run the pipeline
    if python scripts/automation/post_inference_pipeline.py --project_root "$PROJECT_ROOT"; then
        success "Post-inference pipeline completed successfully!"
        return 0
    else
        error "Post-inference pipeline failed!"
        return 1
    fi
}

# Send notification (if possible)
send_notification() {
    local message="$1"
    local status="$2"
    
    # Try to write to a notification file
    local notification_file="$PROJECT_ROOT/INFERENCE_NOTIFICATION.txt"
    
    cat > "$notification_file" << EOF
INFERENCE PIPELINE NOTIFICATION
===============================
Timestamp: $(date)
Job ID: $JOB_ID
Status: $status
Message: $message

Details:
- Project: $PROJECT_ROOT
- Check logs in: $PROJECT_ROOT/logs/
- Results in: $PROJECT_ROOT/evaluation_results/
===============================
EOF
    
    log "Notification saved to: $notification_file"
    
    # Try to send email if mail command is available
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "Inference Pipeline $status" <EMAIL> 2>/dev/null || true
    fi
}

# Main execution
main() {
    log "Starting watch and process script for job $JOB_ID"
    
    # Check if job exists
    if ! check_job_exists; then
        error "Job $JOB_ID not found!"
        exit 1
    fi
    
    # Wait for completion
    if wait_for_completion; then
        # Check final status
        if check_final_status; then
            # Run pipeline
            if run_pipeline; then
                send_notification "Inference and post-processing completed successfully!" "SUCCESS"
                success "All done! 🎉"
                exit 0
            else
                send_notification "Post-processing pipeline failed" "PIPELINE_FAILED"
                error "Pipeline failed"
                exit 1
            fi
        else
            send_notification "Job completed but may have failed" "JOB_FAILED"
            warning "Job may have failed, but attempting pipeline anyway..."
            
            # Try to run pipeline anyway
            if run_pipeline; then
                send_notification "Pipeline succeeded despite job issues" "PARTIAL_SUCCESS"
                warning "Pipeline completed despite job issues"
                exit 0
            else
                send_notification "Both job and pipeline failed" "TOTAL_FAILURE"
                error "Both job and pipeline failed"
                exit 1
            fi
        fi
    else
        send_notification "Timeout waiting for job completion" "TIMEOUT"
        error "Timeout waiting for job completion"
        exit 1
    fi
}

# Handle signals
cleanup() {
    log "Script interrupted. Cleaning up..."
    exit 130
}

trap cleanup INT TERM

# Parse arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [JOB_ID]"
    echo ""
    echo "Monitor SLURM job and run post-inference pipeline when complete"
    echo ""
    echo "Arguments:"
    echo "  JOB_ID    SLURM job ID to monitor (default: 2609520)"
    echo ""
    echo "Environment variables:"
    echo "  CHECK_INTERVAL    Check interval in seconds (default: 300)"
    echo "  MAX_WAIT_TIME     Maximum wait time in seconds (default: 14400)"
    exit 0
fi

# Override defaults with environment variables
CHECK_INTERVAL=${CHECK_INTERVAL:-300}
MAX_WAIT_TIME=${MAX_WAIT_TIME:-14400}

# Run main function
main
