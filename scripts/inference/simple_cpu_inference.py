#!/usr/bin/env python3
"""
Simple CPU Inference
Usa modelli più semplici che funzionano su CPU senza dipendenze complesse
"""

import os
import json
import torch
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration, Blip2Processor, Blip2ForConditionalGeneration
import gc
from datetime import datetime
import argparse
from tqdm import tqdm

class SimpleCPUInference:
    def __init__(self, dataset_path, output_dir):
        self.dataset_path = dataset_path
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Load dataset
        with open(dataset_path, 'r') as f:
            self.dataset = json.load(f)
        
        print(f"📊 Loaded dataset: {len(self.dataset)} images")
        print(f"📁 Output directory: {output_dir}")
    
    def run_blip1_cpu(self):
        """Run BLIP-1 inference on CPU (più semplice di Florence-2)"""
        print("\n🚀 Starting BLIP-1 inference on CPU...")
        
        # Load model on CPU
        print("📥 Loading BLIP-1 model...")
        processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
        model = BlipForConditionalGeneration.from_pretrained(
            "Salesforce/blip-image-captioning-base",
            torch_dtype=torch.float32,
            device_map="cpu"
        )
        
        results = []
        blip1_output = os.path.join(self.output_dir, f"blip1_cpu_results_{self.timestamp}.json")
        
        print("🔄 Processing images...")
        for i, item in enumerate(tqdm(self.dataset, desc="BLIP-1")):
            try:
                image_path = item['image_path']
                
                # Try different path variations
                if not os.path.exists(image_path):
                    alt_paths = [
                        image_path.replace('baseline_t7_images_colors_fixed/', 'data/processed/baseline_t7_images_colors_fixed/'),
                        os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                    ]
                    
                    for alt_path in alt_paths:
                        if os.path.exists(alt_path):
                            image_path = alt_path
                            break
                
                if not os.path.exists(image_path):
                    print(f"⚠️  Image not found: {image_path}")
                    results.append({
                        'image_path': item['image_path'],
                        'ground_truth': item['ground_truth'],
                        'generated_caption': "ERROR: Image not found"
                    })
                    continue
                
                # Load and process image
                image = Image.open(image_path).convert('RGB')
                
                # Resize for faster processing
                if image.size[0] > 384 or image.size[1] > 384:
                    image.thumbnail((384, 384), Image.Resampling.LANCZOS)
                
                # Generate caption
                inputs = processor(image, return_tensors="pt")
                
                with torch.no_grad():
                    out = model.generate(**inputs, max_new_tokens=50, num_beams=2)
                
                caption = processor.decode(out[0], skip_special_tokens=True)
                
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['ground_truth'],
                    'generated_caption': caption
                })
                
                # Save intermediate results every 50 images
                if (i + 1) % 50 == 0:
                    with open(blip1_output + f".tmp_{i+1}", 'w') as f:
                        json.dump(results, f, indent=2)
                    print(f"💾 Saved intermediate results: {i+1}/{len(self.dataset)}")
                
                # Clean memory every 20 images
                if (i + 1) % 20 == 0:
                    gc.collect()
                
            except Exception as e:
                print(f"❌ Error processing {image_path}: {e}")
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['ground_truth'],
                    'generated_caption': f"ERROR: {str(e)}"
                })
        
        # Save final results
        with open(blip1_output, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ BLIP-1 completed: {len(results)} results saved to {blip1_output}")
        
        # Clean up model
        del model, processor
        gc.collect()
        
        return blip1_output, len(results)
    
    def run_blip2_cpu(self):
        """Run BLIP-2 inference on CPU"""
        print("\n🚀 Starting BLIP-2 inference on CPU...")
        
        try:
            # Load model on CPU
            print("📥 Loading BLIP-2 model...")
            processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b")
            model = Blip2ForConditionalGeneration.from_pretrained(
                "Salesforce/blip2-opt-2.7b", 
                torch_dtype=torch.float32,
                device_map="cpu"
            )
            
            results = []
            blip2_output = os.path.join(self.output_dir, f"blip2_cpu_results_{self.timestamp}.json")
            
            print("🔄 Processing images...")
            for i, item in enumerate(tqdm(self.dataset, desc="BLIP-2")):
                try:
                    image_path = item['image_path']
                    
                    # Try different path variations
                    if not os.path.exists(image_path):
                        alt_paths = [
                            image_path.replace('baseline_t7_images_colors_fixed/', 'data/processed/baseline_t7_images_colors_fixed/'),
                            os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                        ]
                        
                        for alt_path in alt_paths:
                            if os.path.exists(alt_path):
                                image_path = alt_path
                                break
                    
                    if not os.path.exists(image_path):
                        print(f"⚠️  Image not found: {image_path}")
                        results.append({
                            'image_path': item['image_path'],
                            'ground_truth': item['ground_truth'],
                            'generated_caption': "ERROR: Image not found"
                        })
                        continue
                    
                    # Load and process image
                    image = Image.open(image_path).convert('RGB')
                    
                    # Resize for faster processing
                    if image.size[0] > 384 or image.size[1] > 384:
                        image.thumbnail((384, 384), Image.Resampling.LANCZOS)
                    
                    # Generate caption
                    inputs = processor(images=image, text="The image depicts", return_tensors="pt")
                    
                    with torch.no_grad():
                        generated_ids = model.generate(
                            **inputs, 
                            max_new_tokens=50,
                            num_beams=2,
                            do_sample=False
                        )
                    
                    generated_text = processor.decode(generated_ids[0], skip_special_tokens=True)
                    
                    results.append({
                        'image_path': item['image_path'],
                        'ground_truth': item['ground_truth'],
                        'generated_caption': generated_text
                    })
                    
                    # Save intermediate results every 50 images
                    if (i + 1) % 50 == 0:
                        with open(blip2_output + f".tmp_{i+1}", 'w') as f:
                            json.dump(results, f, indent=2)
                        print(f"💾 Saved intermediate results: {i+1}/{len(self.dataset)}")
                    
                    # Clean memory every 20 images
                    if (i + 1) % 20 == 0:
                        gc.collect()
                    
                except Exception as e:
                    print(f"❌ Error processing {image_path}: {e}")
                    results.append({
                        'image_path': item['image_path'],
                        'ground_truth': item['ground_truth'],
                        'generated_caption': f"ERROR: {str(e)}"
                    })
            
            # Save final results
            with open(blip2_output, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"✅ BLIP-2 completed: {len(results)} results saved to {blip2_output}")
            
            # Clean up model
            del model, processor
            gc.collect()
            
            return blip2_output, len(results)
            
        except Exception as e:
            print(f"❌ BLIP-2 failed to load: {e}")
            print("⚠️  Skipping BLIP-2...")
            return None, 0
    
    def run_both_models(self):
        """Run both models sequentially"""
        print("🚀 Starting simple CPU baseline inference")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # Run BLIP-1 (più semplice)
        blip1_file, blip1_count = self.run_blip1_cpu()
        
        # Try BLIP-2
        blip2_file, blip2_count = self.run_blip2_cpu()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Generate summary
        summary = {
            'timestamp': self.timestamp,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'dataset_size': len(self.dataset),
            'results': {
                'blip1': {
                    'file': blip1_file,
                    'count': blip1_count
                },
                'blip2': {
                    'file': blip2_file,
                    'count': blip2_count
                }
            }
        }
        
        summary_file = os.path.join(self.output_dir, f"simple_cpu_inference_summary_{self.timestamp}.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print("\n" + "=" * 60)
        print("🎉 SIMPLE CPU BASELINE INFERENCE COMPLETED!")
        print("=" * 60)
        print(f"⏱️  Duration: {duration}")
        print(f"📊 Dataset: {len(self.dataset)} images")
        print(f"✅ BLIP-1: {blip1_count} captions")
        if blip2_count > 0:
            print(f"✅ BLIP-2: {blip2_count} captions")
        else:
            print(f"⚠️  BLIP-2: Failed to run")
        print(f"📁 Output: {self.output_dir}")
        print(f"📄 Summary: {summary_file}")
        print("=" * 60)
        
        return summary

def main():
    parser = argparse.ArgumentParser(description='Simple CPU Baseline Inference')
    parser.add_argument('--dataset', required=True, help='Dataset JSON file')
    parser.add_argument('--output_dir', required=True, help='Output directory')
    parser.add_argument('--model', choices=['blip1', 'blip2', 'both'], default='both',
                       help='Which model to run')
    
    args = parser.parse_args()
    
    # Create inference runner
    runner = SimpleCPUInference(args.dataset, args.output_dir)
    
    # Run inference
    if args.model == 'blip1':
        runner.run_blip1_cpu()
    elif args.model == 'blip2':
        runner.run_blip2_cpu()
    else:
        runner.run_both_models()

if __name__ == "__main__":
    main()
