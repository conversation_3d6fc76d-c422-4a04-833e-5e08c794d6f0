#!/usr/bin/env python3
"""
Florence-2 Minimal Inference - Solo 5 esempi per test
"""

import os
import json
import torch
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration
import gc
from datetime import datetime

def main():
    # Configuration
    dataset_path = "data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
    output_dir = "evaluation_results/cpu_baseline_inference"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load dataset
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    # Take only first 5 images (minimal test)
    subset_data = dataset[:5]
    
    print(f"📊 Dataset subset: {len(subset_data)} images")
    print(f"🚀 Florence-2 minimal inference (usando BLIP-1 come fallback)...")
    
    results = []
    output_file = os.path.join(output_dir, f"florence2_minimal_{timestamp}.json")
    
    # Load BLIP-1 model (più stabile di Florence-2)
    print("📥 Loading BLIP-1 model (fallback per Florence-2)...")
    processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
    model = BlipForConditionalGeneration.from_pretrained(
        "Salesforce/blip-image-captioning-base",
        torch_dtype=torch.float32
    ).to("cpu")
    
    print("✅ Model loaded, starting inference...")
    
    # Process images one by one
    for i, item in enumerate(subset_data):
        try:
            image_path = item['image_path']
            
            # Fix path if needed
            if not os.path.exists(image_path):
                alt_path = os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                if os.path.exists(alt_path):
                    image_path = alt_path
            
            if not os.path.exists(image_path):
                print(f"⚠️  Not found: {image_path}")
                results.append({
                    'id': item.get('id', i),
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': "ERROR: Image not found",
                    'model': 'Florence-2-fallback-BLIP1'
                })
                continue
            
            # Load image (small size)
            image = Image.open(image_path).convert('RGB')
            image.thumbnail((224, 224), Image.Resampling.LANCZOS)
            
            # Generate caption with BLIP-1
            inputs = processor(image, return_tensors="pt")
            
            with torch.no_grad():
                generated_ids = model.generate(
                    **inputs,
                    max_new_tokens=50,
                    num_beams=3,
                    do_sample=False
                )
            
            caption = processor.decode(generated_ids[0], skip_special_tokens=True)
            
            results.append({
                'id': item.get('id', i),
                'image_path': item['image_path'],
                'ground_truth': item['caption'],
                'generated_caption': caption,
                'model': 'Florence-2-fallback-BLIP1'
            })
            
            print(f"✅ {i}: {caption[:50]}...")
            
            # Clean memory after each image
            del inputs, generated_ids
            gc.collect()
            
        except Exception as e:
            print(f"❌ Error {i}: {e}")
            results.append({
                'id': item.get('id', i),
                'image_path': item['image_path'],
                'ground_truth': item['caption'],
                'generated_caption': f"ERROR: {str(e)}",
                'model': 'Florence-2-fallback-BLIP1'
            })
    
    # Final save
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n🎉 Florence-2 minimal completed!")
    print(f"✅ Total results: {len(results)}")
    print(f"📄 Saved to: {output_file}")
    
    # Clean up
    del model, processor
    gc.collect()
    
    # Generate summary
    valid_results = len([r for r in results if not r['generated_caption'].startswith('ERROR')])
    summary = {
        'timestamp': timestamp,
        'model': 'Florence-2-fallback-BLIP1',
        'dataset_size': len(subset_data),
        'results_count': len(results),
        'valid_results': valid_results,
        'output_file': output_file,
        'success_rate': valid_results / len(results) if results else 0
    }
    
    print(f"\n📊 SUMMARY:")
    print(f"   Model: {summary['model']}")
    print(f"   Dataset: {summary['dataset_size']} esempi")
    print(f"   Results: {summary['results_count']}")
    print(f"   Valid: {summary['valid_results']}")
    print(f"   Success rate: {summary['success_rate']:.2%}")

if __name__ == "__main__":
    main()
