#!/bin/bash
#SBATCH --job-name=BASELINE_MIN
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=2
#SBATCH --gres=gpu:1
#SBATCH --mem=8G
#SBATCH --time=08:00:00
#SBATCH --output=logs/baseline_min_%j.out
#SBATCH --error=logs/baseline_min_%j.err

# Minimal Resource Baseline Inference Job
# Configurazione ottimizzata per partire prima

set -e

# Configuration
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
PROJECT_ROOT="/work/tesi_ediluzio"
DATASET_PATH="$PROJECT_ROOT/data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
OUTPUT_DIR="$PROJECT_ROOT/evaluation_results/minimal_inference_$TIMESTAMP"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Setup environment
setup_environment() {
    log "Setting up minimal resource environment..."
    
    cd "$PROJECT_ROOT"
    mkdir -p "$OUTPUT_DIR"
    mkdir -p logs
    
    # Load modules if available
    if command -v module &> /dev/null; then
        module load python/3.9 || true
        module load cuda/11.8 || true
    fi
    
    # Activate conda environment
    if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
        conda activate base || true
    fi
    
    # Minimal environment variables
    export TOKENIZERS_PARALLELISM=false
    export TRANSFORMERS_CACHE="$PROJECT_ROOT/.cache/huggingface"
    export HF_HOME="$PROJECT_ROOT/.cache/huggingface"
    export CUDA_LAUNCH_BLOCKING=0
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
    
    # Check GPU
    if command -v nvidia-smi &> /dev/null; then
        log "GPU Information:"
        nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    fi
    
    success "Environment setup completed"
}

# Install minimal dependencies
install_dependencies() {
    log "Installing minimal dependencies..."
    
    # Only install what's absolutely necessary
    pip install --quiet --no-deps torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    pip install --quiet transformers accelerate
    pip install --quiet pillow tqdm
    
    success "Dependencies installed"
}

# Run Florence-2 with minimal settings
run_florence2_minimal() {
    log "Starting Florence-2 inference (minimal settings)..."
    
    FLORENCE2_OUTPUT="$OUTPUT_DIR/florence2_results_$TIMESTAMP.json"
    
    # Create minimal inference script inline
    cat > "$OUTPUT_DIR/florence2_minimal.py" << 'EOF'
import torch
import json
from PIL import Image
from transformers import AutoProcessor, AutoModelForCausalLM
import gc
import os

def run_minimal_florence2(dataset_path, output_path, device="cuda"):
    print("Loading Florence-2 model...")
    
    # Load with minimal memory usage
    processor = AutoProcessor.from_pretrained("microsoft/Florence-2-base", trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(
        "microsoft/Florence-2-base", 
        torch_dtype=torch.float16,
        trust_remote_code=True,
        device_map=device
    )
    
    # Load dataset
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    results = []
    
    for i, item in enumerate(dataset):
        if i % 50 == 0:
            print(f"Processing {i}/{len(dataset)}")
            gc.collect()
            torch.cuda.empty_cache()
        
        try:
            image_path = item['image_path']
            if not os.path.exists(image_path):
                # Try alternative path
                image_path = image_path.replace('baseline_t7_images_colors_fixed/', 'data/processed/baseline_t7_images_colors_fixed/')
            
            image = Image.open(image_path).convert('RGB')
            
            # Resize to reduce memory usage
            if image.size[0] > 512 or image.size[1] > 512:
                image.thumbnail((512, 512), Image.Resampling.LANCZOS)
            
            inputs = processor(text="<MORE_DETAILED_CAPTION>", images=image, return_tensors="pt").to(device)
            
            with torch.no_grad():
                generated_ids = model.generate(
                    input_ids=inputs["input_ids"],
                    pixel_values=inputs["pixel_values"],
                    max_new_tokens=100,  # Reduced
                    num_beams=2,  # Reduced
                    do_sample=False
                )
            
            generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
            parsed_answer = processor.post_process_generation(
                generated_text, 
                task="<MORE_DETAILED_CAPTION>", 
                image_size=(image.width, image.height)
            )
            
            caption = parsed_answer.get("<MORE_DETAILED_CAPTION>", "")
            
            results.append({
                'image_path': item['image_path'],
                'ground_truth': item['ground_truth'],
                'generated_caption': caption
            })
            
            # Save intermediate results every 100 images
            if (i + 1) % 100 == 0:
                with open(output_path + f".tmp_{i+1}", 'w') as f:
                    json.dump(results, f, indent=2)
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            results.append({
                'image_path': item['image_path'],
                'ground_truth': item['ground_truth'],
                'generated_caption': f"ERROR: {str(e)}"
            })
    
    # Save final results
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"Florence-2 completed: {len(results)} results saved to {output_path}")
    return len(results)

if __name__ == "__main__":
    import sys
    dataset_path = sys.argv[1]
    output_path = sys.argv[2]
    device = sys.argv[3] if len(sys.argv) > 3 else "cuda"
    
    count = run_minimal_florence2(dataset_path, output_path, device)
    print(f"COMPLETED: {count} captions generated")
EOF
    
    # Run the minimal script
    python "$OUTPUT_DIR/florence2_minimal.py" "$DATASET_PATH" "$FLORENCE2_OUTPUT" "cuda"
    FLORENCE2_EXIT=$?
    
    if [[ $FLORENCE2_EXIT -eq 0 ]]; then
        success "Florence-2 inference completed"
    else
        error "Florence-2 inference failed"
    fi
    
    # Clear GPU memory
    python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true
    
    return $FLORENCE2_EXIT
}

# Run BLIP-2 with minimal settings
run_blip2_minimal() {
    log "Starting BLIP-2 inference (minimal settings)..."
    
    BLIP2_OUTPUT="$OUTPUT_DIR/blip2_results_$TIMESTAMP.json"
    
    # Create minimal BLIP-2 script
    cat > "$OUTPUT_DIR/blip2_minimal.py" << 'EOF'
import torch
import json
from PIL import Image
from transformers import Blip2Processor, Blip2ForConditionalGeneration
import gc
import os

def run_minimal_blip2(dataset_path, output_path, device="cuda"):
    print("Loading BLIP-2 model...")
    
    # Load with minimal memory usage
    processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b")
    model = Blip2ForConditionalGeneration.from_pretrained(
        "Salesforce/blip2-opt-2.7b", 
        torch_dtype=torch.float16,
        device_map=device
    )
    
    # Load dataset
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    results = []
    
    for i, item in enumerate(dataset):
        if i % 50 == 0:
            print(f"Processing {i}/{len(dataset)}")
            gc.collect()
            torch.cuda.empty_cache()
        
        try:
            image_path = item['image_path']
            if not os.path.exists(image_path):
                # Try alternative path
                image_path = image_path.replace('baseline_t7_images_colors_fixed/', 'data/processed/baseline_t7_images_colors_fixed/')
            
            image = Image.open(image_path).convert('RGB')
            
            # Resize to reduce memory usage
            if image.size[0] > 512 or image.size[1] > 512:
                image.thumbnail((512, 512), Image.Resampling.LANCZOS)
            
            inputs = processor(images=image, text="The image depicts", return_tensors="pt").to(device)
            
            with torch.no_grad():
                generated_ids = model.generate(
                    **inputs, 
                    max_new_tokens=50,  # Reduced
                    num_beams=2  # Reduced
                )
            
            generated_text = processor.decode(generated_ids[0], skip_special_tokens=True)
            
            results.append({
                'image_path': item['image_path'],
                'ground_truth': item['ground_truth'],
                'generated_caption': generated_text
            })
            
            # Save intermediate results every 100 images
            if (i + 1) % 100 == 0:
                with open(output_path + f".tmp_{i+1}", 'w') as f:
                    json.dump(results, f, indent=2)
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            results.append({
                'image_path': item['image_path'],
                'ground_truth': item['ground_truth'],
                'generated_caption': f"ERROR: {str(e)}"
            })
    
    # Save final results
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"BLIP-2 completed: {len(results)} results saved to {output_path}")
    return len(results)

if __name__ == "__main__":
    import sys
    dataset_path = sys.argv[1]
    output_path = sys.argv[2]
    device = sys.argv[3] if len(sys.argv) > 3 else "cuda"
    
    count = run_minimal_blip2(dataset_path, output_path, device)
    print(f"COMPLETED: {count} captions generated")
EOF
    
    # Run the minimal script
    python "$OUTPUT_DIR/blip2_minimal.py" "$DATASET_PATH" "$BLIP2_OUTPUT" "cuda"
    BLIP2_EXIT=$?
    
    if [[ $BLIP2_EXIT -eq 0 ]]; then
        success "BLIP-2 inference completed"
    else
        error "BLIP-2 inference failed"
    fi
    
    # Clear GPU memory
    python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true
    
    return $BLIP2_EXIT
}

# Generate report
generate_report() {
    log "Generating minimal inference report..."
    
    FLORENCE2_RESULTS="$OUTPUT_DIR/florence2_results_$TIMESTAMP.json"
    BLIP2_RESULTS="$OUTPUT_DIR/blip2_results_$TIMESTAMP.json"
    
    cat > "$OUTPUT_DIR/minimal_inference_summary.txt" << EOF
MINIMAL BASELINE INFERENCE SUMMARY
==================================
Job ID: $SLURM_JOB_ID
Timestamp: $TIMESTAMP
Node: $SLURM_NODELIST
Dataset: $DATASET_PATH
Output Directory: $OUTPUT_DIR

Results:
EOF
    
    if [[ -f "$FLORENCE2_RESULTS" ]]; then
        FLORENCE2_COUNT=$(python -c "import json; print(len(json.load(open('$FLORENCE2_RESULTS'))))" 2>/dev/null || echo "error")
        echo "Florence-2: SUCCESS ($FLORENCE2_COUNT captions)" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
        success "Florence-2: $FLORENCE2_COUNT captions"
    else
        echo "Florence-2: FAILED" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
        error "Florence-2 failed"
    fi
    
    if [[ -f "$BLIP2_RESULTS" ]]; then
        BLIP2_COUNT=$(python -c "import json; print(len(json.load(open('$BLIP2_RESULTS'))))" 2>/dev/null || echo "error")
        echo "BLIP-2: SUCCESS ($BLIP2_COUNT captions)" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
        success "BLIP-2: $BLIP2_COUNT captions"
    else
        echo "BLIP-2: FAILED" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
        error "BLIP-2 failed"
    fi
    
    echo "" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
    echo "Next Steps:" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
    echo "1. Run: python scripts/automation/post_inference_pipeline.py" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
    echo "2. Check: $OUTPUT_DIR/" >> "$OUTPUT_DIR/minimal_inference_summary.txt"
    
    log "Report saved: $OUTPUT_DIR/minimal_inference_summary.txt"
}

# Main execution
main() {
    log "Starting minimal baseline inference job..."
    log "Job ID: $SLURM_JOB_ID"
    log "Node: $SLURM_NODELIST"
    
    setup_environment
    install_dependencies
    
    # Run inference sequentially with minimal resources
    run_florence2_minimal
    FLORENCE2_EXIT=$?
    
    run_blip2_minimal
    BLIP2_EXIT=$?
    
    generate_report
    
    EXIT_CODE=$((FLORENCE2_EXIT + BLIP2_EXIT))
    
    if [[ $EXIT_CODE -eq 0 ]]; then
        success "Minimal inference job completed successfully!"
    else
        error "Minimal inference job completed with errors"
    fi
    
    exit $EXIT_CODE
}

# Cleanup
cleanup() {
    log "Cleaning up..."
    python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true
}

trap cleanup EXIT
trap 'error "Job interrupted"; exit 130' INT TERM

# Run main
main
