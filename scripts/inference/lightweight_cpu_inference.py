#!/usr/bin/env python3
"""
Lightweight CPU Inference
Versione ottimizzata per memoria limitata
"""

import os
import json
import torch
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration
import gc
from datetime import datetime
import argparse
from tqdm import tqdm

class LightweightCPUInference:
    def __init__(self, dataset_path, output_dir):
        self.dataset_path = dataset_path
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Load dataset
        with open(dataset_path, 'r') as f:
            self.dataset = json.load(f)
        
        print(f"📊 Loaded dataset: {len(self.dataset)} images")
        print(f"📁 Output directory: {output_dir}")
    
    def run_blip1_lightweight(self):
        """Run BLIP-1 with aggressive memory management"""
        print("\n🚀 Starting BLIP-1 inference (lightweight mode)...")
        
        results = []
        blip1_output = os.path.join(self.output_dir, f"blip1_lightweight_{self.timestamp}.json")
        
        # Process in small batches to avoid memory issues
        batch_size = 10
        total_batches = (len(self.dataset) + batch_size - 1) // batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(self.dataset))
            batch_items = self.dataset[start_idx:end_idx]
            
            print(f"\n📦 Processing batch {batch_idx + 1}/{total_batches} (images {start_idx}-{end_idx-1})")
            
            # Load model fresh for each batch to avoid memory accumulation
            print("📥 Loading BLIP-1 model...")
            processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
            model = BlipForConditionalGeneration.from_pretrained(
                "Salesforce/blip-image-captioning-base",
                torch_dtype=torch.float32,
                device_map="cpu"
            )
            
            # Process batch
            batch_results = []
            for i, item in enumerate(tqdm(batch_items, desc=f"Batch {batch_idx+1}")):
                try:
                    image_path = item['image_path']
                    
                    # Try different path variations
                    if not os.path.exists(image_path):
                        alt_paths = [
                            image_path.replace('baseline_t7_images_colors_fixed/', 'data/processed/baseline_t7_images_colors_fixed/'),
                            os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                        ]
                        
                        for alt_path in alt_paths:
                            if os.path.exists(alt_path):
                                image_path = alt_path
                                break
                    
                    if not os.path.exists(image_path):
                        print(f"⚠️  Image not found: {image_path}")
                        batch_results.append({
                            'image_path': item['image_path'],
                            'ground_truth': item['caption'],
                            'generated_caption': "ERROR: Image not found"
                        })
                        continue
                    
                    # Load and process image with aggressive resizing
                    image = Image.open(image_path).convert('RGB')
                    
                    # Resize to very small size for memory efficiency
                    if image.size[0] > 224 or image.size[1] > 224:
                        image.thumbnail((224, 224), Image.Resampling.LANCZOS)
                    
                    # Generate caption
                    inputs = processor(image, return_tensors="pt")
                    
                    with torch.no_grad():
                        out = model.generate(**inputs, max_new_tokens=30, num_beams=1, do_sample=False)
                    
                    caption = processor.decode(out[0], skip_special_tokens=True)
                    
                    batch_results.append({
                        'image_path': item['image_path'],
                        'ground_truth': item['caption'],
                        'generated_caption': caption
                    })
                    
                    # Clean memory after each image
                    del inputs, out
                    gc.collect()
                    
                except Exception as e:
                    print(f"❌ Error processing {image_path}: {e}")
                    batch_results.append({
                        'image_path': item['image_path'],
                        'ground_truth': item['caption'],
                        'generated_caption': f"ERROR: {str(e)}"
                    })
            
            # Add batch results to total
            results.extend(batch_results)
            
            # Save intermediate results after each batch
            with open(blip1_output + f".batch_{batch_idx+1}", 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"💾 Saved batch {batch_idx+1}: {len(results)} total results")
            
            # Clean up model completely
            del model, processor
            gc.collect()
            
            # Force garbage collection
            import time
            time.sleep(1)
        
        # Save final results
        with open(blip1_output, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ BLIP-1 lightweight completed: {len(results)} results saved to {blip1_output}")
        return blip1_output, len(results)
    
    def run_simple_test(self, num_images=5):
        """Run a simple test with just a few images"""
        print(f"\n🧪 Running simple test with {num_images} images...")
        
        # Load model
        print("📥 Loading BLIP-1 model...")
        processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
        model = BlipForConditionalGeneration.from_pretrained(
            "Salesforce/blip-image-captioning-base",
            torch_dtype=torch.float32,
            device_map="cpu"
        )
        
        results = []
        test_output = os.path.join(self.output_dir, f"blip1_test_{self.timestamp}.json")
        
        # Process only first few images
        test_items = self.dataset[:num_images]
        
        for i, item in enumerate(tqdm(test_items, desc="Test")):
            try:
                image_path = item['image_path']
                
                # Try different path variations
                if not os.path.exists(image_path):
                    alt_paths = [
                        image_path.replace('baseline_t7_images_colors_fixed/', 'data/processed/baseline_t7_images_colors_fixed/'),
                        os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                    ]
                    
                    for alt_path in alt_paths:
                        if os.path.exists(alt_path):
                            image_path = alt_path
                            break
                
                if not os.path.exists(image_path):
                    print(f"⚠️  Image not found: {image_path}")
                    continue
                
                # Load and process image
                image = Image.open(image_path).convert('RGB')
                image.thumbnail((224, 224), Image.Resampling.LANCZOS)
                
                # Generate caption
                inputs = processor(image, return_tensors="pt")
                
                with torch.no_grad():
                    out = model.generate(**inputs, max_new_tokens=30, num_beams=1)
                
                caption = processor.decode(out[0], skip_special_tokens=True)
                
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': caption
                })
                
                print(f"✅ Image {i+1}: {caption[:50]}...")
                
            except Exception as e:
                print(f"❌ Error processing image {i+1}: {e}")
        
        # Save test results
        with open(test_output, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"🧪 Test completed: {len(results)} results saved to {test_output}")
        
        # Clean up
        del model, processor
        gc.collect()
        
        return test_output, len(results)

def main():
    parser = argparse.ArgumentParser(description='Lightweight CPU Baseline Inference')
    parser.add_argument('--dataset', required=True, help='Dataset JSON file')
    parser.add_argument('--output_dir', required=True, help='Output directory')
    parser.add_argument('--mode', choices=['test', 'full'], default='full',
                       help='Run mode: test (5 images) or full (all images)')
    
    args = parser.parse_args()
    
    # Create inference runner
    runner = LightweightCPUInference(args.dataset, args.output_dir)
    
    # Run inference
    if args.mode == 'test':
        runner.run_simple_test(5)
    else:
        runner.run_blip1_lightweight()

if __name__ == "__main__":
    main()
