#!/bin/bash
#SBATCH --job-name=BASELINE_1GPU
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --gres=gpu:1
#SBATCH --mem=16G
#SBATCH --time=06:00:00
#SBATCH --output=logs/baseline_1gpu_%j.out
#SBATCH --error=logs/baseline_1gpu_%j.err

# Production Baseline Inference Job (Single GPU)
# Esegue Florence-2 e BLIP-2 sequenzialmente su una GPU

set -e

# Configuration
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
PROJECT_ROOT="/work/tesi_ediluzio"
DATASET_PATH="$PROJECT_ROOT/data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
OUTPUT_DIR="$PROJECT_ROOT/evaluation_results/production_inference_$TIMESTAMP"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Setup environment
setup_environment() {
    log "Setting up SLURM environment (Single GPU)..."
    
    # Change to project directory
    cd "$PROJECT_ROOT"
    
    # Create output and logs directories
    mkdir -p "$OUTPUT_DIR"
    mkdir -p logs
    
    # Load modules if available
    if command -v module &> /dev/null; then
        module load python/3.9 || true
        module load cuda/11.8 || true
    fi
    
    # Activate conda environment if available
    if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
        conda activate base || true
    fi
    
    # Set environment variables
    export TOKENIZERS_PARALLELISM=false
    export TRANSFORMERS_CACHE="$PROJECT_ROOT/.cache/huggingface"
    export HF_HOME="$PROJECT_ROOT/.cache/huggingface"
    export CUDA_LAUNCH_BLOCKING=0
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
    
    # Check GPU availability
    if command -v nvidia-smi &> /dev/null; then
        log "GPU Information:"
        nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    fi
    
    success "Environment setup completed"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    # Update pip
    pip install --upgrade pip --quiet
    
    # Install core dependencies
    pip install --quiet torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    pip install --quiet transformers accelerate
    pip install --quiet pillow tqdm
    
    success "Dependencies installed"
}

# Run Florence-2 inference
run_florence2() {
    log "Starting Florence-2 inference..."
    
    FLORENCE2_OUTPUT="$OUTPUT_DIR/florence2_results_$TIMESTAMP.json"
    
    python scripts/inference/florence2_production_inference.py \
        --dataset "$DATASET_PATH" \
        --output "$FLORENCE2_OUTPUT" \
        --device "cuda" \
        --model "microsoft/Florence-2-large"
    
    FLORENCE2_EXIT=$?
    
    if [[ $FLORENCE2_EXIT -eq 0 ]]; then
        success "Florence-2 inference completed successfully"
    else
        error "Florence-2 inference failed with exit code $FLORENCE2_EXIT"
    fi
    
    # Clear GPU memory
    python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true
    
    return $FLORENCE2_EXIT
}

# Run BLIP-2 inference
run_blip2() {
    log "Starting BLIP-2 inference..."
    
    BLIP2_OUTPUT="$OUTPUT_DIR/blip2_results_$TIMESTAMP.json"
    
    python scripts/inference/blip2_production_inference.py \
        --dataset "$DATASET_PATH" \
        --output "$BLIP2_OUTPUT" \
        --device "cuda" \
        --model "Salesforce/blip2-opt-2.7b"
    
    BLIP2_EXIT=$?
    
    if [[ $BLIP2_EXIT -eq 0 ]]; then
        success "BLIP-2 inference completed successfully"
    else
        error "BLIP-2 inference failed with exit code $BLIP2_EXIT"
    fi
    
    # Clear GPU memory
    python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true
    
    return $BLIP2_EXIT
}

# Generate final report
generate_report() {
    log "Generating final report..."
    
    FLORENCE2_RESULTS="$OUTPUT_DIR/florence2_results_$TIMESTAMP.json"
    BLIP2_RESULTS="$OUTPUT_DIR/blip2_results_$TIMESTAMP.json"
    
    # Create summary report
    cat > "$OUTPUT_DIR/inference_summary_$TIMESTAMP.txt" << EOF
PRODUCTION BASELINE INFERENCE SUMMARY (Single GPU)
==================================================
Job ID: $SLURM_JOB_ID
Timestamp: $TIMESTAMP
Node: $SLURM_NODELIST
Dataset: $DATASET_PATH
Output Directory: $OUTPUT_DIR

Results:
EOF
    
    if [[ -f "$FLORENCE2_RESULTS" ]]; then
        FLORENCE2_COUNT=$(python -c "import json; print(len(json.load(open('$FLORENCE2_RESULTS'))))" 2>/dev/null || echo "error")
        echo "Florence-2: SUCCESS ($FLORENCE2_COUNT captions)" >> "$OUTPUT_DIR/inference_summary_$TIMESTAMP.txt"
        success "Florence-2 results: $FLORENCE2_RESULTS ($FLORENCE2_COUNT captions)"
    else
        echo "Florence-2: FAILED" >> "$OUTPUT_DIR/inference_summary_$TIMESTAMP.txt"
        error "Florence-2 results not found"
    fi
    
    if [[ -f "$BLIP2_RESULTS" ]]; then
        BLIP2_COUNT=$(python -c "import json; print(len(json.load(open('$BLIP2_RESULTS'))))" 2>/dev/null || echo "error")
        echo "BLIP-2: SUCCESS ($BLIP2_COUNT captions)" >> "$OUTPUT_DIR/inference_summary_$TIMESTAMP.txt"
        success "BLIP-2 results: $BLIP2_RESULTS ($BLIP2_COUNT captions)"
    else
        echo "BLIP-2: FAILED" >> "$OUTPUT_DIR/inference_summary_$TIMESTAMP.txt"
        error "BLIP-2 results not found"
    fi
    
    cat >> "$OUTPUT_DIR/inference_summary_$TIMESTAMP.txt" << EOF

Next Steps:
1. Calculate metrics: python scripts/evaluation/CALCULATE_ALL_REAL_METRICS.py
2. Generate radar charts: python scripts/visualization/create_professional_radar_charts.py

Files Generated:
- Florence-2: $FLORENCE2_RESULTS
- BLIP-2: $BLIP2_RESULTS
- Summary: $OUTPUT_DIR/inference_summary_$TIMESTAMP.txt
==================================================
EOF
    
    log "Summary report saved to: $OUTPUT_DIR/inference_summary_$TIMESTAMP.txt"
}

# Cleanup function
cleanup() {
    log "Cleaning up..."
    
    # Clear GPU memory
    python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true
}

# Signal handlers
trap cleanup EXIT
trap 'error "Job interrupted"; exit 130' INT TERM

# Main execution
main() {
    log "Starting SLURM production inference job (Single GPU)..."
    log "Job ID: $SLURM_JOB_ID"
    log "Node: $SLURM_NODELIST"
    
    setup_environment
    install_dependencies
    
    # Run inference sequentially
    run_florence2
    FLORENCE2_EXIT=$?
    
    run_blip2
    BLIP2_EXIT=$?
    
    generate_report
    
    EXIT_CODE=$((FLORENCE2_EXIT + BLIP2_EXIT))
    
    if [[ $EXIT_CODE -eq 0 ]]; then
        success "Production inference job completed successfully!"
    else
        error "Production inference job completed with errors (exit code: $EXIT_CODE)"
    fi
    
    exit $EXIT_CODE
}

# Run main function
main
