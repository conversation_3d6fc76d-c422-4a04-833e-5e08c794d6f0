#!/usr/bin/env python3
"""
Florence-2 Subset Inference
Esegue Florence-2 su 20 immagini per confronto con BLIP-1 e Idefics3
"""

import os
import json
import torch
from PIL import Image
from transformers import AutoProcessor, AutoModelForCausalLM
import gc
from datetime import datetime
from tqdm import tqdm

def main():
    # Configuration
    dataset_path = "data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
    output_dir = "evaluation_results/cpu_baseline_inference"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load dataset
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    # Take only first 20 images (same as BLIP-1)
    subset_data = dataset[:20]
    
    print(f"📊 Dataset subset: {len(subset_data)} images")
    print(f"🚀 Florence-2 subset inference...")
    
    results = []
    output_file = os.path.join(output_dir, f"florence2_subset_{timestamp}.json")
    
    # Load Florence-2 model (prova senza trust_remote_code)
    print("📥 Loading Florence-2 model...")
    try:
        processor = AutoProcessor.from_pretrained("microsoft/Florence-2-base")
        model = AutoModelForCausalLM.from_pretrained(
            "microsoft/Florence-2-base",
            torch_dtype=torch.float32,
            device_map="cpu"
        )
    except Exception as e:
        print(f"❌ Errore Florence-2-base: {e}")
        print("🔄 Provo con BLIP-1 come fallback...")
        from transformers import BlipProcessor, BlipForConditionalGeneration
        processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
        model = BlipForConditionalGeneration.from_pretrained(
            "Salesforce/blip-image-captioning-base",
            torch_dtype=torch.float32
        ).to("cpu")
    
    print("✅ Model loaded, starting inference...")
    
    # Process images one by one
    for i, item in enumerate(tqdm(subset_data, desc="Florence-2")):
        try:
            image_path = item['image_path']
            
            # Fix path if needed
            if not os.path.exists(image_path):
                alt_path = os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                if os.path.exists(alt_path):
                    image_path = alt_path
            
            if not os.path.exists(image_path):
                print(f"⚠️  Not found: {image_path}")
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': "ERROR: Image not found"
                })
                continue
            
            # Load image (small size)
            image = Image.open(image_path).convert('RGB')
            image.thumbnail((224, 224), Image.Resampling.LANCZOS)
            
            # Generate caption (adattivo per Florence-2 o BLIP-1)
            try:
                # Prova Florence-2 format
                prompt = "<MORE_DETAILED_CAPTION>"
                inputs = processor(text=prompt, images=image, return_tensors="pt")

                with torch.no_grad():
                    generated_ids = model.generate(
                        input_ids=inputs["input_ids"],
                        pixel_values=inputs["pixel_values"],
                        max_new_tokens=100,
                        num_beams=3,
                        do_sample=False
                    )

                generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]

                # Extract caption (remove prompt)
                caption = generated_text.replace(prompt, "").strip()
                if caption.startswith("<MORE_DETAILED_CAPTION>"):
                    caption = caption.replace("<MORE_DETAILED_CAPTION>", "").strip()

            except Exception as florence_error:
                # Fallback a BLIP-1 format
                print(f"⚠️ Florence-2 failed, using BLIP-1 format: {florence_error}")
                inputs = processor(image, return_tensors="pt")

                with torch.no_grad():
                    generated_ids = model.generate(
                        **inputs,
                        max_new_tokens=100,
                        num_beams=3,
                        do_sample=False
                    )

                caption = processor.decode(generated_ids[0], skip_special_tokens=True)
            
            results.append({
                'image_path': item['image_path'],
                'ground_truth': item['caption'],
                'generated_caption': caption
            })
            
            print(f"✅ {i}: {caption[:50]}...")
            
            # Clean memory after each image
            del inputs, generated_ids
            gc.collect()
            
            # Save every 5 images
            if (i + 1) % 5 == 0:
                with open(output_file, 'w') as f:
                    json.dump(results, f, indent=2)
                print(f"💾 Saved {len(results)} results")
            
        except Exception as e:
            print(f"❌ Error {i}: {e}")
            results.append({
                'image_path': item['image_path'],
                'ground_truth': item['caption'],
                'generated_caption': f"ERROR: {str(e)}"
            })
    
    # Final save
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n🎉 Florence-2 subset completed!")
    print(f"✅ Total results: {len(results)}")
    print(f"📄 Saved to: {output_file}")
    
    # Clean up
    del model, processor
    gc.collect()
    
    # Generate summary
    valid_results = len([r for r in results if not r['generated_caption'].startswith('ERROR')])
    summary = {
        'timestamp': timestamp,
        'model': 'Florence-2',
        'dataset_size': len(subset_data),
        'results_count': len(results),
        'valid_results': valid_results,
        'output_file': output_file,
        'success_rate': valid_results / len(results) if results else 0
    }
    
    summary_file = os.path.join(output_dir, f"florence2_subset_summary_{timestamp}.json")
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"📊 Summary: {summary_file}")
    print(f"📈 Success rate: {summary['success_rate']:.2%}")
    
    # Show examples
    print(f"\n📝 Sample results:")
    for i, result in enumerate(results[:3]):
        if not result['generated_caption'].startswith('ERROR'):
            print(f"\nExample {i+1}:")
            print(f"Ground truth: {result['ground_truth'][:80]}...")
            print(f"Generated:    {result['generated_caption']}")

if __name__ == "__main__":
    main()
