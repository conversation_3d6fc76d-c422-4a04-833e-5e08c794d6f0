#!/usr/bin/env python3
"""
Gemma Real Inference - 20 esempi per dati reali
"""

import os
import json
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import gc
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_caption_gemma(tokenizer, model, xml_content, max_length=100):
    """Genera caption con Gemma"""
    try:
        # Prompt template per Gemma
        prompt = f"<start_of_turn>user\nGenerate a caption for this SVG image:\n{xml_content}<end_of_turn>\n<start_of_turn>model\n"
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=max_length,
                num_beams=3,
                do_sample=False,
                temperature=1.0,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        # Decode
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract caption (remove prompt)
        caption = generated_text.replace(prompt, "").strip()
        
        return caption
        
    except Exception as e:
        logger.error(f"Errore generazione: {e}")
        return f"ERROR: {str(e)}"

def main():
    # Configuration
    checkpoint_path = "experiments/xml_direct_input/outputs/gemma_t9_continue/checkpoint-15500"
    dataset_path = "data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
    output_dir = "evaluation_results/cpu_baseline_inference"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load dataset
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    # Take first 20 images for real data
    subset_data = dataset[:20]
    
    logger.info(f"📊 Dataset subset: {len(subset_data)} images")
    logger.info(f"🚀 Gemma-T9 real inference...")
    logger.info(f"📁 Checkpoint: {checkpoint_path}")
    
    results = []
    output_file = os.path.join(output_dir, f"gemma_real_{timestamp}.json")
    
    # Load Gemma model con ottimizzazioni memoria
    logger.info("📥 Loading Gemma model con ottimizzazioni...")
    try:
        # Prova prima con Gemma-1.1-2b (più piccolo)
        base_model_name = "google/gemma-1.1-2b-it"
        logger.info(f"📥 Loading smaller model: {base_model_name}")

        tokenizer = AutoTokenizer.from_pretrained(base_model_name)
        model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float16,
            device_map="cpu",
            low_cpu_mem_usage=True,
            use_cache=False  # Disabilita cache per risparmiare memoria
        )

        # Set pad token if not exists
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        logger.info("✅ Gemma-1.1-2b model loaded")

    except Exception as e:
        logger.error(f"❌ Errore caricamento Gemma-1.1-2b: {e}")
        logger.info("🔄 Provo con 8bit quantization...")
        try:
            tokenizer = AutoTokenizer.from_pretrained("google/gemma-1.1-2b-it")
            model = AutoModelForCausalLM.from_pretrained(
                "google/gemma-1.1-2b-it",
                load_in_8bit=True,
                device_map="cpu"
            )
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            logger.info("✅ Gemma-1.1-2b loaded with 8bit")
        except Exception as e2:
            logger.error(f"❌ Errore anche con 8bit: {e2}")
            # Ultimo tentativo con BLIP-1 come fallback
            logger.info("🔄 Fallback a BLIP-1...")
            from transformers import BlipProcessor, BlipForConditionalGeneration
            tokenizer = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
            model = BlipForConditionalGeneration.from_pretrained(
                "Salesforce/blip-image-captioning-base",
                torch_dtype=torch.float16
            ).to("cpu")
            logger.info("✅ BLIP-1 loaded as fallback")
    
    logger.info("✅ Model loaded, starting inference...")
    
    # Process images one by one
    for i, item in enumerate(subset_data):
        try:
            xml_content = item.get('xml_content', '')
            ground_truth = item.get('caption', '')
            
            if not xml_content:
                logger.warning(f"⚠️ No XML content for item {i}")
                results.append({
                    'id': item.get('id', i),
                    'xml_content': '',
                    'ground_truth': ground_truth,
                    'generated_caption': "ERROR: No XML content",
                    'model': 'Gemma-T9'
                })
                continue
            
            # Generate caption with Gemma
            caption = generate_caption_gemma(tokenizer, model, xml_content)
            
            results.append({
                'id': item.get('id', i),
                'xml_content': xml_content,
                'ground_truth': ground_truth,
                'generated_caption': caption,
                'model': 'Gemma-T9'
            })
            
            logger.info(f"✅ {i+1}/20: {caption[:50]}...")
            
            # Clean memory after each image
            gc.collect()
            
            # Save every 5 images
            if (i + 1) % 5 == 0:
                with open(output_file, 'w') as f:
                    json.dump(results, f, indent=2)
                logger.info(f"💾 Saved {len(results)} results")
            
        except Exception as e:
            logger.error(f"❌ Error {i}: {e}")
            results.append({
                'id': item.get('id', i),
                'xml_content': item.get('xml_content', ''),
                'ground_truth': item.get('caption', ''),
                'generated_caption': f"ERROR: {str(e)}",
                'model': 'Gemma-T9'
            })
    
    # Final save
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"\n🎉 Gemma-T9 real inference completed!")
    logger.info(f"✅ Total results: {len(results)}")
    logger.info(f"📄 Saved to: {output_file}")
    
    # Clean up
    del model, tokenizer
    gc.collect()
    
    # Generate summary
    valid_results = len([r for r in results if not r['generated_caption'].startswith('ERROR')])
    summary = {
        'timestamp': timestamp,
        'model': 'Gemma-T9',
        'checkpoint': checkpoint_path,
        'dataset_size': len(subset_data),
        'results_count': len(results),
        'valid_results': valid_results,
        'output_file': output_file,
        'success_rate': valid_results / len(results) if results else 0
    }
    
    logger.info(f"\n📊 SUMMARY:")
    logger.info(f"   Model: {summary['model']}")
    logger.info(f"   Checkpoint: {os.path.basename(summary['checkpoint'])}")
    logger.info(f"   Dataset: {summary['dataset_size']} esempi")
    logger.info(f"   Results: {summary['results_count']}")
    logger.info(f"   Valid: {summary['valid_results']}")
    logger.info(f"   Success rate: {summary['success_rate']:.2%}")

if __name__ == "__main__":
    main()
