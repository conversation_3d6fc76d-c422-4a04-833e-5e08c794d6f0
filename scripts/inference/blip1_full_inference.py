#!/usr/bin/env python3
"""
BLIP-1 Full Inference
Esegue BLIP-1 su tutte le 400 immagini con gestione memoria ottimizzata
"""

import os
import json
import torch
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration
import gc
from datetime import datetime
from tqdm import tqdm

def main():
    # Configuration
    dataset_path = "data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
    output_dir = "evaluation_results/cpu_baseline_inference"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load dataset
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    print(f"📊 Loaded dataset: {len(dataset)} images")
    print(f"📁 Output directory: {output_dir}")
    print(f"🚀 Starting BLIP-1 full inference...")
    
    results = []
    blip1_output = os.path.join(output_dir, f"blip1_full_{timestamp}.json")
    
    # Process in small batches to avoid memory issues
    batch_size = 20
    total_batches = (len(dataset) + batch_size - 1) // batch_size
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(dataset))
        batch_items = dataset[start_idx:end_idx]
        
        print(f"\n📦 Processing batch {batch_idx + 1}/{total_batches} (images {start_idx}-{end_idx-1})")
        
        # Load model fresh for each batch
        print("📥 Loading BLIP-1 model...")
        processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
        model = BlipForConditionalGeneration.from_pretrained(
            "Salesforce/blip-image-captioning-base",
            torch_dtype=torch.float32,
            device_map="cpu"
        )
        
        # Process batch
        batch_results = []
        for i, item in enumerate(tqdm(batch_items, desc=f"Batch {batch_idx+1}")):
            try:
                image_path = item['image_path']
                
                # Try different path variations
                if not os.path.exists(image_path):
                    alt_paths = [
                        image_path.replace('baseline_t7_images_colors_fixed/', 'data/processed/baseline_t7_images_colors_fixed/'),
                        os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                    ]
                    
                    for alt_path in alt_paths:
                        if os.path.exists(alt_path):
                            image_path = alt_path
                            break
                
                if not os.path.exists(image_path):
                    print(f"⚠️  Image not found: {image_path}")
                    batch_results.append({
                        'image_path': item['image_path'],
                        'ground_truth': item['caption'],
                        'generated_caption': "ERROR: Image not found"
                    })
                    continue
                
                # Load and process image with aggressive resizing
                image = Image.open(image_path).convert('RGB')
                
                # Resize to small size for memory efficiency
                if image.size[0] > 224 or image.size[1] > 224:
                    image.thumbnail((224, 224), Image.Resampling.LANCZOS)
                
                # Generate caption
                inputs = processor(image, return_tensors="pt")
                
                with torch.no_grad():
                    out = model.generate(**inputs, max_new_tokens=50, num_beams=2, do_sample=False)
                
                caption = processor.decode(out[0], skip_special_tokens=True)
                
                batch_results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': caption
                })
                
                # Clean memory after each image
                del inputs, out
                gc.collect()
                
            except Exception as e:
                print(f"❌ Error processing {image_path}: {e}")
                batch_results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': f"ERROR: {str(e)}"
                })
        
        # Add batch results to total
        results.extend(batch_results)
        
        # Save intermediate results after each batch
        with open(blip1_output + f".batch_{batch_idx+1}", 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"💾 Saved batch {batch_idx+1}: {len(results)} total results")
        
        # Clean up model completely
        del model, processor
        gc.collect()
        
        # Force garbage collection and wait
        import time
        time.sleep(2)
    
    # Save final results
    with open(blip1_output, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n🎉 BLIP-1 full inference completed!")
    print(f"✅ Total results: {len(results)}")
    print(f"📄 Saved to: {blip1_output}")
    
    # Generate summary
    summary = {
        'timestamp': timestamp,
        'model': 'BLIP-1',
        'dataset_size': len(dataset),
        'results_count': len(results),
        'output_file': blip1_output,
        'success_rate': len([r for r in results if not r['generated_caption'].startswith('ERROR')]) / len(results)
    }
    
    summary_file = os.path.join(output_dir, f"blip1_summary_{timestamp}.json")
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"📊 Summary saved to: {summary_file}")
    print(f"📈 Success rate: {summary['success_rate']:.2%}")

if __name__ == "__main__":
    main()
