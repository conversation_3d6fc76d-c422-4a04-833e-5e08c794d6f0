#!/usr/bin/env python3
"""
Production Inference Orchestrator
Lancia Florence-2 e BLIP-2 in parallelo su GPU multiple
"""

import os
import subprocess
import time
import json
from datetime import datetime
import argparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionInferenceOrchestrator:
    def __init__(self, dataset_path, output_dir):
        self.dataset_path = dataset_path
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
    def run_parallel_inference(self):
        """Run Florence-2 and BLIP-2 inference in parallel"""
        logger.info("Starting parallel inference for Florence-2 and BLIP-2")
        
        # Define output paths
        florence2_output = os.path.join(
            self.output_dir, 
            f"florence2_results_{self.timestamp}.json"
        )
        blip2_output = os.path.join(
            self.output_dir, 
            f"blip2_results_{self.timestamp}.json"
        )
        
        # Define commands
        florence2_cmd = [
            "python", "scripts/inference/florence2_production_inference.py",
            "--dataset", self.dataset_path,
            "--output", florence2_output,
            "--device", "cuda:0"  # Use first GPU
        ]
        
        blip2_cmd = [
            "python", "scripts/inference/blip2_production_inference.py",
            "--dataset", self.dataset_path,
            "--output", blip2_output,
            "--device", "cuda:1"  # Use second GPU
        ]
        
        # Start processes
        logger.info("Starting Florence-2 inference on GPU 0...")
        florence2_process = subprocess.Popen(
            florence2_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=dict(os.environ, CUDA_VISIBLE_DEVICES="0")
        )
        
        logger.info("Starting BLIP-2 inference on GPU 1...")
        blip2_process = subprocess.Popen(
            blip2_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=dict(os.environ, CUDA_VISIBLE_DEVICES="1")
        )
        
        # Monitor processes
        processes = {
            "Florence-2": florence2_process,
            "BLIP-2": blip2_process
        }
        
        completed = {}
        
        while processes:
            for name, process in list(processes.items()):
                if process.poll() is not None:
                    # Process completed
                    stdout, stderr = process.communicate()
                    completed[name] = {
                        'return_code': process.returncode,
                        'stdout': stdout,
                        'stderr': stderr
                    }
                    
                    if process.returncode == 0:
                        logger.info(f"{name} inference completed successfully")
                    else:
                        logger.error(f"{name} inference failed with return code {process.returncode}")
                        logger.error(f"Error: {stderr}")
                    
                    del processes[name]
            
            if processes:
                time.sleep(30)  # Check every 30 seconds
        
        # Generate summary report
        self._generate_summary_report(completed, florence2_output, blip2_output)
        
        return completed
    
    def run_sequential_inference(self):
        """Run Florence-2 and BLIP-2 inference sequentially (fallback)"""
        logger.info("Starting sequential inference for Florence-2 and BLIP-2")
        
        # Define output paths
        florence2_output = os.path.join(
            self.output_dir, 
            f"florence2_results_{self.timestamp}.json"
        )
        blip2_output = os.path.join(
            self.output_dir, 
            f"blip2_results_{self.timestamp}.json"
        )
        
        results = {}
        
        # Run Florence-2
        logger.info("Running Florence-2 inference...")
        florence2_cmd = [
            "python", "scripts/inference/florence2_production_inference.py",
            "--dataset", self.dataset_path,
            "--output", florence2_output,
            "--device", "auto"
        ]
        
        florence2_result = subprocess.run(
            florence2_cmd,
            capture_output=True,
            text=True
        )
        
        results["Florence-2"] = {
            'return_code': florence2_result.returncode,
            'stdout': florence2_result.stdout,
            'stderr': florence2_result.stderr
        }
        
        if florence2_result.returncode == 0:
            logger.info("Florence-2 inference completed successfully")
        else:
            logger.error(f"Florence-2 inference failed: {florence2_result.stderr}")
        
        # Run BLIP-2
        logger.info("Running BLIP-2 inference...")
        blip2_cmd = [
            "python", "scripts/inference/blip2_production_inference.py",
            "--dataset", self.dataset_path,
            "--output", blip2_output,
            "--device", "auto"
        ]
        
        blip2_result = subprocess.run(
            blip2_cmd,
            capture_output=True,
            text=True
        )
        
        results["BLIP-2"] = {
            'return_code': blip2_result.returncode,
            'stdout': blip2_result.stdout,
            'stderr': blip2_result.stderr
        }
        
        if blip2_result.returncode == 0:
            logger.info("BLIP-2 inference completed successfully")
        else:
            logger.error(f"BLIP-2 inference failed: {blip2_result.stderr}")
        
        # Generate summary report
        self._generate_summary_report(results, florence2_output, blip2_output)
        
        return results
    
    def _generate_summary_report(self, results, florence2_output, blip2_output):
        """Generate summary report"""
        report = {
            'timestamp': self.timestamp,
            'dataset_path': self.dataset_path,
            'output_directory': self.output_dir,
            'results': results,
            'output_files': {
                'florence2': florence2_output if os.path.exists(florence2_output) else None,
                'blip2': blip2_output if os.path.exists(blip2_output) else None
            }
        }
        
        # Add file statistics
        for model, output_path in report['output_files'].items():
            if output_path and os.path.exists(output_path):
                try:
                    with open(output_path, 'r') as f:
                        data = json.load(f)
                    report['output_files'][f'{model}_count'] = len(data)
                except:
                    report['output_files'][f'{model}_count'] = 'error'
        
        # Save report
        report_path = os.path.join(self.output_dir, f"inference_report_{self.timestamp}.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Summary report saved to: {report_path}")
        
        # Print summary
        print("\n" + "="*60)
        print("PRODUCTION INFERENCE SUMMARY")
        print("="*60)
        print(f"Timestamp: {self.timestamp}")
        print(f"Dataset: {self.dataset_path}")
        print(f"Output Directory: {self.output_dir}")
        print("\nResults:")
        for model, result in results.items():
            status = "SUCCESS" if result['return_code'] == 0 else "FAILED"
            print(f"  {model}: {status}")
        print(f"\nReport: {report_path}")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description='Production Inference Orchestrator')
    parser.add_argument('--dataset', required=True, help='Path to dataset JSON file')
    parser.add_argument('--output_dir', required=True, help='Output directory for results')
    parser.add_argument('--mode', choices=['parallel', 'sequential'], default='parallel',
                       help='Inference mode (parallel or sequential)')
    
    args = parser.parse_args()
    
    # Initialize orchestrator
    orchestrator = ProductionInferenceOrchestrator(
        dataset_path=args.dataset,
        output_dir=args.output_dir
    )
    
    # Run inference
    if args.mode == 'parallel':
        try:
            results = orchestrator.run_parallel_inference()
        except Exception as e:
            logger.error(f"Parallel inference failed: {e}")
            logger.info("Falling back to sequential inference...")
            results = orchestrator.run_sequential_inference()
    else:
        results = orchestrator.run_sequential_inference()
    
    # Check if any inference succeeded
    success_count = sum(1 for result in results.values() if result['return_code'] == 0)
    
    if success_count > 0:
        logger.info(f"Inference completed. {success_count}/{len(results)} models succeeded.")
    else:
        logger.error("All inference processes failed!")
        exit(1)

if __name__ == "__main__":
    main()
