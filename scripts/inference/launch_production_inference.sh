#!/bin/bash
# Production Inference Launcher
# Ottimizzato per ambiente di produzione con gestione risorse

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Default paths
DATASET_PATH="$PROJECT_ROOT/data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
OUTPUT_DIR="$PROJECT_ROOT/evaluation_results/production_inference_$TIMESTAMP"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_ROOT/requirements.txt" ]] && [[ ! -f "$PROJECT_ROOT/pyproject.toml" ]]; then
        error "Not in project root directory. Current: $(pwd), Project root: $PROJECT_ROOT"
        warning "Continuing anyway..."
    fi
    
    # Check Python
    if ! command -v python &> /dev/null; then
        error "Python not found. Please ensure Python is installed and in PATH."
        exit 1
    fi
    
    # Check GPU availability
    if command -v nvidia-smi &> /dev/null; then
        GPU_COUNT=$(nvidia-smi --list-gpus | wc -l)
        log "Found $GPU_COUNT GPU(s)"
        nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    else
        warning "nvidia-smi not found. Will use CPU inference."
        GPU_COUNT=0
    fi
    
    # Check dataset
    if [[ ! -f "$DATASET_PATH" ]]; then
        error "Dataset not found at: $DATASET_PATH"
        exit 1
    fi
    
    # Check dataset size
    DATASET_SIZE=$(python -c "import json; print(len(json.load(open('$DATASET_PATH'))))")
    log "Dataset contains $DATASET_SIZE images"
    
    success "System requirements check passed"
}

# Setup environment
setup_environment() {
    log "Setting up environment..."
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    log "Output directory: $OUTPUT_DIR"
    
    # Set environment variables for optimal performance
    export TOKENIZERS_PARALLELISM=false
    export TRANSFORMERS_CACHE="$PROJECT_ROOT/.cache/huggingface"
    export HF_HOME="$PROJECT_ROOT/.cache/huggingface"
    
    # GPU memory optimization
    if [[ $GPU_COUNT -gt 0 ]]; then
        export CUDA_LAUNCH_BLOCKING=0
        export CUDA_CACHE_DISABLE=0
        export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    fi
    
    success "Environment setup completed"
}

# Install dependencies
install_dependencies() {
    log "Installing/updating dependencies..."
    
    # Install required packages
    pip install --quiet --upgrade torch torchvision torchaudio
    pip install --quiet --upgrade transformers accelerate
    pip install --quiet --upgrade pillow tqdm
    
    success "Dependencies installed"
}

# Run inference
run_inference() {
    log "Starting production inference..."
    
    cd "$PROJECT_ROOT"
    
    # Determine inference mode based on GPU availability
    if [[ $GPU_COUNT -ge 2 ]]; then
        INFERENCE_MODE="parallel"
        log "Using parallel inference mode (multiple GPUs)"
    else
        INFERENCE_MODE="sequential"
        log "Using sequential inference mode (single GPU/CPU)"
    fi
    
    # Run the orchestrator
    python scripts/inference/run_production_inference.py \
        --dataset "$DATASET_PATH" \
        --output_dir "$OUTPUT_DIR" \
        --mode "$INFERENCE_MODE"
    
    INFERENCE_EXIT_CODE=$?
    
    if [[ $INFERENCE_EXIT_CODE -eq 0 ]]; then
        success "Inference completed successfully"
    else
        error "Inference failed with exit code $INFERENCE_EXIT_CODE"
        exit $INFERENCE_EXIT_CODE
    fi
}

# Generate final report
generate_report() {
    log "Generating final report..."
    
    # Find result files
    FLORENCE2_RESULTS=$(find "$OUTPUT_DIR" -name "florence2_results_*.json" | head -1)
    BLIP2_RESULTS=$(find "$OUTPUT_DIR" -name "blip2_results_*.json" | head -1)
    
    echo ""
    echo "=========================================="
    echo "PRODUCTION INFERENCE COMPLETED"
    echo "=========================================="
    echo "Timestamp: $TIMESTAMP"
    echo "Output Directory: $OUTPUT_DIR"
    echo ""
    
    if [[ -f "$FLORENCE2_RESULTS" ]]; then
        FLORENCE2_COUNT=$(python -c "import json; print(len(json.load(open('$FLORENCE2_RESULTS'))))" 2>/dev/null || echo "error")
        echo "Florence-2 Results: $FLORENCE2_RESULTS ($FLORENCE2_COUNT captions)"
    else
        echo "Florence-2 Results: NOT FOUND"
    fi
    
    if [[ -f "$BLIP2_RESULTS" ]]; then
        BLIP2_COUNT=$(python -c "import json; print(len(json.load(open('$BLIP2_RESULTS'))))" 2>/dev/null || echo "error")
        echo "BLIP-2 Results: $BLIP2_RESULTS ($BLIP2_COUNT captions)"
    else
        echo "BLIP-2 Results: NOT FOUND"
    fi
    
    echo ""
    echo "Next steps:"
    echo "1. Run metrics calculation: python scripts/evaluation/CALCULATE_ALL_REAL_METRICS.py"
    echo "2. Generate radar charts: python scripts/visualization/create_professional_radar_charts.py"
    echo "=========================================="
}

# Cleanup function
cleanup() {
    log "Cleaning up..."
    
    # Kill any remaining processes
    pkill -f "florence2_production_inference.py" 2>/dev/null || true
    pkill -f "blip2_production_inference.py" 2>/dev/null || true
    
    # Clear GPU memory
    if command -v nvidia-smi &> /dev/null; then
        python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true
    fi
}

# Signal handlers
trap cleanup EXIT
trap 'error "Script interrupted"; exit 130' INT TERM

# Main execution
main() {
    log "Starting production inference pipeline..."
    
    check_requirements
    setup_environment
    install_dependencies
    run_inference
    generate_report
    
    success "Production inference pipeline completed successfully!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dataset)
            DATASET_PATH="$2"
            shift 2
            ;;
        --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [--dataset PATH] [--output DIR]"
            echo ""
            echo "Options:"
            echo "  --dataset PATH    Path to dataset JSON file"
            echo "  --output DIR      Output directory for results"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run main function
main
