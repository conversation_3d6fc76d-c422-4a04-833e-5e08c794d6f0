#!/usr/bin/env python3
"""
BLIP-1 Minimal Memory Inference
Batch size 5, ricarica modello ogni volta
"""

import os
import json
import torch
from PIL import Image
from transformers import BlipProcessor, BlipForConditionalGeneration
import gc
from datetime import datetime
from tqdm import tqdm

def main():
    # Configuration
    dataset_path = "data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
    output_dir = "evaluation_results/cpu_baseline_inference"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load dataset
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    print(f"📊 Dataset: {len(dataset)} images")
    print(f"🚀 BLIP-1 minimal memory inference...")
    
    results = []
    output_file = os.path.join(output_dir, f"blip1_minimal_{timestamp}.json")
    
    # Process in very small batches
    batch_size = 5
    total_batches = (len(dataset) + batch_size - 1) // batch_size
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(dataset))
        batch_items = dataset[start_idx:end_idx]
        
        print(f"\n📦 Batch {batch_idx + 1}/{total_batches} ({start_idx}-{end_idx-1})")
        
        # Load model fresh for each batch
        processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
        model = BlipForConditionalGeneration.from_pretrained(
            "Salesforce/blip-image-captioning-base",
            torch_dtype=torch.float32,
            device_map="cpu"
        )
        
        # Process batch
        for i, item in enumerate(batch_items):
            try:
                image_path = item['image_path']
                
                # Fix path if needed
                if not os.path.exists(image_path):
                    alt_path = os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                    if os.path.exists(alt_path):
                        image_path = alt_path
                
                if not os.path.exists(image_path):
                    print(f"⚠️  Not found: {image_path}")
                    results.append({
                        'image_path': item['image_path'],
                        'ground_truth': item['caption'],
                        'generated_caption': "ERROR: Image not found"
                    })
                    continue
                
                # Load image (very small)
                image = Image.open(image_path).convert('RGB')
                image.thumbnail((128, 128), Image.Resampling.LANCZOS)
                
                # Generate caption
                inputs = processor(image, return_tensors="pt")
                
                with torch.no_grad():
                    out = model.generate(**inputs, max_new_tokens=30, num_beams=1)
                
                caption = processor.decode(out[0], skip_special_tokens=True)
                
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': caption
                })
                
                print(f"✅ {start_idx + i}: {caption[:40]}...")
                
                # Clean memory
                del inputs, out
                gc.collect()
                
            except Exception as e:
                print(f"❌ Error {start_idx + i}: {e}")
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': f"ERROR: {str(e)}"
                })
        
        # Save after each batch
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"💾 Saved: {len(results)} results")
        
        # Clean up model
        del model, processor
        gc.collect()
        
        # Wait between batches
        import time
        time.sleep(1)
    
    print(f"\n🎉 Completed: {len(results)} results")
    print(f"📄 File: {output_file}")

if __name__ == "__main__":
    main()
