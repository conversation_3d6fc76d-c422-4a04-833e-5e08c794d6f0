#!/usr/bin/env python3
"""
Florence-2 Production Inference Script
Ottimizzato per ambiente di produzione con gestione memoria e batch processing
"""

import os
import json
import torch
import gc
from datetime import datetime
from PIL import Image
from transformers import AutoProcessor, AutoModelForCausalLM
import argparse
from tqdm import tqdm
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Florence2ProductionInference:
    def __init__(self, model_name="microsoft/Florence-2-large", device="auto", batch_size=1):
        self.model_name = model_name
        self.batch_size = batch_size
        self.device = self._setup_device(device)
        self.model = None
        self.processor = None
        
    def _setup_device(self, device):
        """Setup device with memory optimization"""
        if device == "auto":
            if torch.cuda.is_available():
                device = "cuda"
                # Clear GPU cache
                torch.cuda.empty_cache()
                logger.info(f"Using GPU: {torch.cuda.get_device_name()}")
                logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
            else:
                device = "cpu"
                logger.info("Using CPU")
        return device
    
    def load_model(self):
        """Load model with memory optimization"""
        logger.info(f"Loading Florence-2 model: {self.model_name}")
        
        try:
            # Load processor
            self.processor = AutoProcessor.from_pretrained(
                self.model_name, 
                trust_remote_code=True
            )
            
            # Load model with optimization
            if self.device == "cuda":
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    torch_dtype=torch.float16,  # Use half precision
                    device_map="auto",
                    trust_remote_code=True,
                    low_cpu_mem_usage=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    torch_dtype=torch.float32,
                    trust_remote_code=True,
                    low_cpu_mem_usage=True
                )
                self.model.to(self.device)
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def generate_caption(self, image_path, prompt="<MORE_DETAILED_CAPTION>"):
        """Generate caption for single image"""
        try:
            # Load and process image
            image = Image.open(image_path).convert('RGB')
            
            # Prepare inputs
            inputs = self.processor(
                text=prompt, 
                images=image, 
                return_tensors="pt"
            ).to(self.device)
            
            # Generate with optimized parameters
            with torch.no_grad():
                generated_ids = self.model.generate(
                    input_ids=inputs["input_ids"],
                    pixel_values=inputs["pixel_values"],
                    max_new_tokens=1024,
                    num_beams=3,
                    do_sample=False,
                    temperature=1.0,
                    pad_token_id=self.processor.tokenizer.pad_token_id,
                    eos_token_id=self.processor.tokenizer.eos_token_id,
                )
            
            # Decode response
            generated_text = self.processor.batch_decode(
                generated_ids, 
                skip_special_tokens=False
            )[0]
            
            # Extract caption
            parsed_answer = self.processor.post_process_generation(
                generated_text, 
                task=prompt, 
                image_size=(image.width, image.height)
            )
            
            caption = parsed_answer.get(prompt, "")
            
            # Clean up
            del inputs, generated_ids
            if self.device == "cuda":
                torch.cuda.empty_cache()
            
            return caption
            
        except Exception as e:
            logger.error(f"Error processing image {image_path}: {e}")
            return ""
    
    def process_dataset(self, dataset_path, output_path, start_idx=0, end_idx=None):
        """Process entire dataset with progress tracking"""
        logger.info(f"Loading dataset from: {dataset_path}")
        
        with open(dataset_path, 'r') as f:
            dataset = json.load(f)
        
        if end_idx is None:
            end_idx = len(dataset)
        
        dataset_subset = dataset[start_idx:end_idx]
        logger.info(f"Processing {len(dataset_subset)} images (indices {start_idx}-{end_idx-1})")
        
        results = []
        
        for i, item in enumerate(tqdm(dataset_subset, desc="Processing images")):
            try:
                image_path = item['image_path']
                ground_truth = item.get('caption', '')
                
                # Generate caption
                start_time = datetime.now()
                generated_caption = self.generate_caption(image_path)
                inference_time = (datetime.now() - start_time).total_seconds()
                
                result = {
                    'id': item['id'],
                    'original_id': item.get('original_id', item['id']),
                    'image_path': image_path,
                    'ground_truth': ground_truth,
                    'generated_caption': generated_caption,
                    'inference_time': inference_time,
                    'model': self.model_name
                }
                
                results.append(result)
                
                # Save intermediate results every 50 images
                if (i + 1) % 50 == 0:
                    self._save_intermediate_results(results, output_path, start_idx + i + 1)
                
                # Memory cleanup every 10 images
                if (i + 1) % 10 == 0:
                    gc.collect()
                    if self.device == "cuda":
                        torch.cuda.empty_cache()
                
            except Exception as e:
                logger.error(f"Error processing item {i}: {e}")
                continue
        
        # Save final results
        self._save_results(results, output_path)
        logger.info(f"Inference completed. Results saved to: {output_path}")
        
        return results
    
    def _save_intermediate_results(self, results, output_path, current_idx):
        """Save intermediate results"""
        intermediate_path = output_path.replace('.json', f'_intermediate_{current_idx}.json')
        with open(intermediate_path, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Intermediate results saved: {intermediate_path}")
    
    def _save_results(self, results, output_path):
        """Save final results"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2)

def main():
    parser = argparse.ArgumentParser(description='Florence-2 Production Inference')
    parser.add_argument('--dataset', required=True, help='Path to dataset JSON file')
    parser.add_argument('--output', required=True, help='Output path for results')
    parser.add_argument('--model', default='microsoft/Florence-2-large', help='Model name')
    parser.add_argument('--device', default='auto', help='Device to use (auto/cuda/cpu)')
    parser.add_argument('--batch_size', type=int, default=1, help='Batch size')
    parser.add_argument('--start_idx', type=int, default=0, help='Start index')
    parser.add_argument('--end_idx', type=int, default=None, help='End index')
    
    args = parser.parse_args()
    
    # Initialize inference engine
    inference_engine = Florence2ProductionInference(
        model_name=args.model,
        device=args.device,
        batch_size=args.batch_size
    )
    
    # Load model
    inference_engine.load_model()
    
    # Process dataset
    results = inference_engine.process_dataset(
        dataset_path=args.dataset,
        output_path=args.output,
        start_idx=args.start_idx,
        end_idx=args.end_idx
    )
    
    logger.info(f"Processing completed. Generated {len(results)} captions.")

if __name__ == "__main__":
    main()
