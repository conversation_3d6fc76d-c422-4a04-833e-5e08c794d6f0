#!/usr/bin/env python3
"""
Inference Monitoring Script
Monitora il progresso dell'inference in tempo reale
"""

import os
import time
import json
import subprocess
from datetime import datetime
import argparse

class InferenceMonitor:
    def __init__(self, job_id=None, output_dir=None):
        self.job_id = job_id
        self.output_dir = output_dir
        self.start_time = datetime.now()
        
    def get_job_status(self):
        """Get SLURM job status"""
        if not self.job_id:
            return "No job ID provided"
        
        try:
            result = subprocess.run(
                ['squeue', '-j', str(self.job_id), '--format=%T,%M,%N'],
                capture_output=True, text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # Skip header
                    status_line = lines[1]
                    status, time_running, node = status_line.split(',')
                    return f"Status: {status}, Time: {time_running}, Node: {node}"
            
            return "Job not found or completed"
            
        except Exception as e:
            return f"Error checking job status: {e}"
    
    def check_output_files(self):
        """Check for output files and progress"""
        if not self.output_dir:
            return "No output directory specified"
        
        if not os.path.exists(self.output_dir):
            return "Output directory not found"
        
        results = {}
        
        # Look for result files
        for filename in os.listdir(self.output_dir):
            if filename.endswith('.json') and ('florence2' in filename or 'blip2' in filename):
                filepath = os.path.join(self.output_dir, filename)
                try:
                    with open(filepath, 'r') as f:
                        data = json.load(f)
                    
                    model_name = 'Florence-2' if 'florence2' in filename else 'BLIP-2'
                    results[model_name] = {
                        'file': filename,
                        'count': len(data),
                        'size_mb': os.path.getsize(filepath) / (1024*1024),
                        'modified': datetime.fromtimestamp(os.path.getmtime(filepath))
                    }
                except Exception as e:
                    model_name = 'Florence-2' if 'florence2' in filename else 'BLIP-2'
                    results[model_name] = {'error': str(e)}
        
        # Look for intermediate files
        intermediate_files = [f for f in os.listdir(self.output_dir) if 'intermediate' in f]
        
        return {
            'results': results,
            'intermediate_files': len(intermediate_files),
            'total_files': len(os.listdir(self.output_dir))
        }
    
    def check_log_files(self):
        """Check SLURM log files"""
        if not self.job_id:
            return "No job ID provided"
        
        log_dir = "logs"
        if not os.path.exists(log_dir):
            return "Log directory not found"
        
        log_info = {}
        
        # Check output log
        out_log = f"{log_dir}/baseline_1gpu_{self.job_id}.out"
        if os.path.exists(out_log):
            with open(out_log, 'r') as f:
                lines = f.readlines()
            
            log_info['output'] = {
                'lines': len(lines),
                'size_kb': os.path.getsize(out_log) / 1024,
                'last_lines': lines[-5:] if lines else []
            }
        
        # Check error log
        err_log = f"{log_dir}/baseline_1gpu_{self.job_id}.err"
        if os.path.exists(err_log):
            with open(err_log, 'r') as f:
                lines = f.readlines()
            
            log_info['error'] = {
                'lines': len(lines),
                'size_kb': os.path.getsize(err_log) / 1024,
                'last_lines': lines[-5:] if lines else []
            }
        
        return log_info
    
    def print_status(self):
        """Print comprehensive status"""
        elapsed = datetime.now() - self.start_time
        
        print("=" * 60)
        print(f"INFERENCE MONITORING - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Monitoring time: {elapsed}")
        print("=" * 60)
        
        # Job status
        if self.job_id:
            print(f"Job ID: {self.job_id}")
            print(f"Job Status: {self.get_job_status()}")
            print()
        
        # Output files status
        if self.output_dir:
            print(f"Output Directory: {self.output_dir}")
            output_status = self.check_output_files()
            
            if isinstance(output_status, dict):
                print(f"Total files: {output_status['total_files']}")
                print(f"Intermediate files: {output_status['intermediate_files']}")
                print()
                
                for model, info in output_status['results'].items():
                    if 'error' in info:
                        print(f"{model}: ERROR - {info['error']}")
                    else:
                        print(f"{model}: {info['count']} captions, {info['size_mb']:.1f} MB")
                        print(f"  File: {info['file']}")
                        print(f"  Modified: {info['modified']}")
                    print()
            else:
                print(f"Output Status: {output_status}")
                print()
        
        # Log files status
        if self.job_id:
            print("Log Files:")
            log_status = self.check_log_files()
            
            for log_type, info in log_status.items():
                print(f"  {log_type.upper()}: {info['lines']} lines, {info['size_kb']:.1f} KB")
                if info['last_lines']:
                    print("    Last lines:")
                    for line in info['last_lines'][-3:]:  # Show last 3 lines
                        print(f"      {line.strip()}")
                print()
        
        print("=" * 60)
    
    def monitor(self, interval=30, max_duration=14400):  # 4 hours max
        """Monitor inference progress"""
        print(f"Starting monitoring (interval: {interval}s, max duration: {max_duration}s)")
        
        start_time = time.time()
        
        while time.time() - start_time < max_duration:
            self.print_status()
            
            # Check if job is completed
            if self.job_id:
                job_status = self.get_job_status()
                if "not found or completed" in job_status:
                    print("Job completed or not found. Stopping monitoring.")
                    break
            
            print(f"Waiting {interval} seconds for next check...")
            time.sleep(interval)
        
        print("Monitoring completed.")

def main():
    parser = argparse.ArgumentParser(description='Monitor inference progress')
    parser.add_argument('--job_id', type=int, help='SLURM job ID to monitor')
    parser.add_argument('--output_dir', help='Output directory to monitor')
    parser.add_argument('--interval', type=int, default=30, help='Check interval in seconds')
    parser.add_argument('--max_duration', type=int, default=14400, help='Max monitoring duration in seconds')
    parser.add_argument('--once', action='store_true', help='Check once and exit')
    
    args = parser.parse_args()
    
    monitor = InferenceMonitor(job_id=args.job_id, output_dir=args.output_dir)
    
    if args.once:
        monitor.print_status()
    else:
        monitor.monitor(interval=args.interval, max_duration=args.max_duration)

if __name__ == "__main__":
    main()
