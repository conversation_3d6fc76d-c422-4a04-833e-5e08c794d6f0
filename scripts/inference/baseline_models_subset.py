#!/usr/bin/env python3
"""
Baseline Models Subset Inference
Esegue BLIP-2 e Florence-2 su 20 immagini per confronto reale
"""

import os
import json
import torch
from PIL import Image
import gc
from datetime import datetime
from tqdm import tqdm

def run_blip2_subset():
    """Esegue BLIP-2 su 20 immagini"""
    print("🚀 Starting BLIP-2 subset inference...")
    
    try:
        from transformers import Blip2Processor, Blip2ForConditionalGeneration
        
        # Load dataset
        dataset_path = "data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
        with open(dataset_path, 'r') as f:
            dataset = json.load(f)
        
        subset_data = dataset[:20]
        
        # Load BLIP-2 model (smaller version)
        print("📥 Loading BLIP-2 model...")
        processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b")
        model = Blip2ForConditionalGeneration.from_pretrained(
            "Salesforce/blip2-opt-2.7b",
            torch_dtype=torch.float16,
            device_map="cpu"
        )
        
        results = []
        
        # Process images
        for i, item in enumerate(tqdm(subset_data, desc="BLIP-2")):
            try:
                image_path = item['image_path']
                
                # Fix path
                if not os.path.exists(image_path):
                    alt_path = os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                    if os.path.exists(alt_path):
                        image_path = alt_path
                
                if not os.path.exists(image_path):
                    continue
                
                # Load and resize image
                image = Image.open(image_path).convert('RGB')
                image.thumbnail((224, 224), Image.Resampling.LANCZOS)
                
                # Generate caption
                inputs = processor(image, return_tensors="pt")
                
                with torch.no_grad():
                    generated_ids = model.generate(**inputs, max_new_tokens=30, num_beams=2)
                
                caption = processor.decode(generated_ids[0], skip_special_tokens=True)
                
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': caption
                })
                
                print(f"✅ BLIP-2 {i}: {caption[:40]}...")
                
                # Clean memory
                del inputs, generated_ids
                gc.collect()
                
            except Exception as e:
                print(f"❌ BLIP-2 Error {i}: {e}")
                continue
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "evaluation_results/cpu_baseline_inference"
        os.makedirs(output_dir, exist_ok=True)
        
        output_file = os.path.join(output_dir, f"blip2_subset_{timestamp}.json")
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ BLIP-2 completed: {len(results)} results saved to {output_file}")
        
        # Clean up
        del model, processor
        gc.collect()
        
        return output_file, len(results)
        
    except Exception as e:
        print(f"❌ BLIP-2 failed: {e}")
        return None, 0

def run_florence2_subset():
    """Esegue Florence-2 su 20 immagini - versione semplificata"""
    print("🚀 Starting Florence-2 subset inference...")
    
    try:
        # Prova con transformers standard
        from transformers import AutoProcessor, AutoModelForCausalLM
        
        # Load dataset
        dataset_path = "data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
        with open(dataset_path, 'r') as f:
            dataset = json.load(f)
        
        subset_data = dataset[:20]
        
        # Prova Florence-2-base (più piccolo)
        print("📥 Loading Florence-2-base model...")
        processor = AutoProcessor.from_pretrained("microsoft/Florence-2-base", trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            "microsoft/Florence-2-base",
            torch_dtype=torch.float16,
            device_map="cpu",
            trust_remote_code=True
        )
        
        results = []
        
        # Process images
        for i, item in enumerate(tqdm(subset_data, desc="Florence-2")):
            try:
                image_path = item['image_path']
                
                # Fix path
                if not os.path.exists(image_path):
                    alt_path = os.path.join('data/processed/baseline_t7_images_colors_fixed/', os.path.basename(image_path))
                    if os.path.exists(alt_path):
                        image_path = alt_path
                
                if not os.path.exists(image_path):
                    continue
                
                # Load and resize image
                image = Image.open(image_path).convert('RGB')
                image.thumbnail((224, 224), Image.Resampling.LANCZOS)
                
                # Generate caption
                prompt = "<MORE_DETAILED_CAPTION>"
                inputs = processor(text=prompt, images=image, return_tensors="pt")
                
                with torch.no_grad():
                    generated_ids = model.generate(
                        input_ids=inputs["input_ids"],
                        pixel_values=inputs["pixel_values"],
                        max_new_tokens=50,
                        num_beams=2
                    )
                
                generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
                caption = generated_text.replace(prompt, "").strip()
                
                results.append({
                    'image_path': item['image_path'],
                    'ground_truth': item['caption'],
                    'generated_caption': caption
                })
                
                print(f"✅ Florence-2 {i}: {caption[:40]}...")
                
                # Clean memory
                del inputs, generated_ids
                gc.collect()
                
            except Exception as e:
                print(f"❌ Florence-2 Error {i}: {e}")
                continue
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "evaluation_results/cpu_baseline_inference"
        os.makedirs(output_dir, exist_ok=True)
        
        output_file = os.path.join(output_dir, f"florence2_subset_{timestamp}.json")
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ Florence-2 completed: {len(results)} results saved to {output_file}")
        
        # Clean up
        del model, processor
        gc.collect()
        
        return output_file, len(results)
        
    except Exception as e:
        print(f"❌ Florence-2 failed: {e}")
        return None, 0

def main():
    """Esegue entrambi i modelli"""
    print("🎯 BASELINE MODELS SUBSET INFERENCE")
    print("=" * 50)
    
    # Run BLIP-2
    blip2_file, blip2_count = run_blip2_subset()
    
    print("\n" + "=" * 50)
    
    # Run Florence-2
    florence2_file, florence2_count = run_florence2_subset()
    
    print("\n🎉 SUMMARY:")
    print(f"✅ BLIP-2: {blip2_count} results - {blip2_file}")
    print(f"✅ Florence-2: {florence2_count} results - {florence2_file}")
    
    return blip2_file, florence2_file

if __name__ == "__main__":
    main()
