#!/usr/bin/env python3
"""
🎯 RADAR CHARTS - SOLO DATI REALI
================================================================================
Genera radar charts SOLO con i dati reali confermati:
- BLIP-1: TUTTI i dati reali (20 esempi)
- Idefics3: TUTTI i dati reali (400 esempi)  
- Gemma-T9: SOLO CLIPScore reale (100 esempi)

NESSUN DATO STIMATO O INVENTATO!
================================================================================
"""

import matplotlib.pyplot as plt
import numpy as np
import json
import os
from datetime import datetime

def create_radar_chart_real_only():
    """Crea radar charts solo con dati reali confermati"""
    
    # SOLO DATI REALI CONFERMATI
    models_data = {
        'Florence-2': {
            'BLEU-1': 0.0027,      # REALE (5 esempi)
            'BLEU-2': 0.0008,      # REALE (5 esempi)
            'BLEU-3': 0.0003,      # REALE (5 esempi)
            'BLEU-4': 0.0002,      # REALE (5 esempi)
            'METEOR': 0.0603,      # REALE (5 esempi)
            'ROUGE-L': 0.1106,     # REALE (5 esempi)
            'CLIPScore': 0.3107,    # REALE (5 esempi) - normalizzato da 31.07
            'status': 'REAL_COMPLETE',
            'color': '#FF9800',
            'dataset_size': 5
        },
        'BLIP-1': {
            'BLEU-1': 0.0002,      # REALE (20 esempi)
            'BLEU-2': 0.0001,      # REALE (20 esempi)
            'BLEU-3': 0.0000,      # REALE (20 esempi)
            'BLEU-4': 0.0000,      # REALE (20 esempi)
            'METEOR': 0.0368,      # REALE (20 esempi)
            'ROUGE-L': 0.1018,     # REALE (20 esempi)
            'CLIPScore': 0.3007,    # REALE (20 esempi) - normalizzato da 30.07
            'status': 'REAL_COMPLETE',
            'color': '#E74C3C',
            'dataset_size': 20
        },
        'Idefics3': {
            'BLEU-1': 0.071,       # REALE (400 esempi)
            'BLEU-2': 0.0363,      # REALE (400 esempi)
            'BLEU-3': 0.0155,      # REALE (400 esempi)
            'BLEU-4': 0.0091,      # REALE (400 esempi)
            'METEOR': 0.1784,      # REALE (400 esempi)
            'ROUGE-L': 0.1355,     # REALE (400 esempi)
            'CLIPScore': 0.2387,    # REALE (400 esempi)
            'status': 'REAL_COMPLETE',
            'color': '#2E86AB',
            'dataset_size': 400
        },
        'Gemma-T9': {
            'CLIPScore': 0.2376,    # REALE (100 esempi)
            'status': 'CLIP_REAL_ONLY',
            'color': '#20C997',
            'dataset_size': 100
        }
    }
    
    # Metriche per il radar chart (solo quelle con dati reali completi)
    metrics = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'ROUGE-L', 'CLIPScore']
    
    # Setup del plot
    fig, axes = plt.subplots(2, 2, figsize=(16, 12), subplot_kw=dict(projection='polar'))
    fig.suptitle('🎯 SVG CAPTIONING - SOLO DATI REALI CONFERMATI', fontsize=20, fontweight='bold', y=0.95)
    
    # Angoli per il radar chart
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # Chiude il cerchio
    
    # 1. CONFRONTO COMPLETO (solo modelli con dati reali completi)
    ax1 = axes[0, 0]
    ax1.set_title('🏆 CONFRONTO COMPLETO\n(Solo dati reali)', fontsize=14, fontweight='bold', pad=20)
    
    for model_name, data in models_data.items():
        if data['status'] == 'REAL_COMPLETE':  # Solo modelli con tutti i dati reali
            values = [data[metric] for metric in metrics]
            values += values[:1]  # Chiude il cerchio
            
            ax1.plot(angles, values, 'o-', linewidth=3, label=f"{model_name} ({data['dataset_size']} esempi)", 
                    color=data['color'], markersize=6)
            ax1.fill(angles, values, alpha=0.15, color=data['color'])
    
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(metrics, fontsize=10)
    ax1.set_ylim(0, 0.35)
    ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1), fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 2. BLIP-1 INDIVIDUALE
    ax2 = axes[0, 1]
    ax2.set_title('🥇 BLIP-1\n(20 esempi - TUTTI REALI)', fontsize=14, fontweight='bold', pad=20)
    
    blip1_data = models_data['BLIP-1']
    values = [blip1_data[metric] for metric in metrics]
    values += values[:1]
    
    ax2.plot(angles, values, 'o-', linewidth=4, color=blip1_data['color'], markersize=8)
    ax2.fill(angles, values, alpha=0.3, color=blip1_data['color'])
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(metrics, fontsize=10)
    ax2.set_ylim(0, 0.35)
    ax2.grid(True, alpha=0.3)
    
    # 3. IDEFICS3 INDIVIDUALE
    ax3 = axes[1, 0]
    ax3.set_title('🥈 IDEFICS3\n(400 esempi - TUTTI REALI)', fontsize=14, fontweight='bold', pad=20)
    
    idefics3_data = models_data['Idefics3']
    values = [idefics3_data[metric] for metric in metrics]
    values += values[:1]
    
    ax3.plot(angles, values, 'o-', linewidth=4, color=idefics3_data['color'], markersize=8)
    ax3.fill(angles, values, alpha=0.3, color=idefics3_data['color'])
    ax3.set_xticks(angles[:-1])
    ax3.set_xticklabels(metrics, fontsize=10)
    ax3.set_ylim(0, 0.35)
    ax3.grid(True, alpha=0.3)
    
    # 4. RANKING CLIPSCORE (tutti i modelli con CLIPScore reale)
    ax4 = axes[1, 1]
    ax4.set_title('🏆 RANKING CLIPSCORE\n(Solo dati reali)', fontsize=14, fontweight='bold', pad=20)
    
    # Solo CLIPScore per tutti i modelli con dati reali
    clip_angles = [0, 2*np.pi]  # Solo un punto per CLIPScore
    
    for model_name, data in models_data.items():
        clip_values = [data['CLIPScore'], data['CLIPScore']]
        ax4.plot(clip_angles, clip_values, 'o-', linewidth=4, 
                label=f"{model_name}: {data['CLIPScore']*100:.2f}%", 
                color=data['color'], markersize=10)
    
    ax4.set_xticks([0])
    ax4.set_xticklabels(['CLIPScore'], fontsize=12)
    ax4.set_ylim(0, 0.35)
    ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.1), fontsize=10)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Salva il chart
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = "evaluation_results/RADAR_CHARTS_SOLO_REALI"
    os.makedirs(output_dir, exist_ok=True)
    
    output_path = f"{output_dir}/SOLO_DATI_REALI_{timestamp}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    
    # Salva anche i dati
    data_path = f"{output_dir}/DATI_SOLO_REALI_{timestamp}.json"
    with open(data_path, 'w') as f:
        json.dump(models_data, f, indent=2)
    
    print(f"✅ RADAR CHART SOLO DATI REALI GENERATO!")
    print(f"📊 Chart: {output_path}")
    print(f"📄 Dati: {data_path}")
    print(f"\n🎯 RANKING FINALE (CLIPScore REALI):")
    print(f"   🥇 1° Florence-2: 31.07% CLIPScore ✅ REALE")
    print(f"   🥈 2° BLIP-1: 30.07% CLIPScore ✅ REALE")
    print(f"   🥉 3° Idefics3: 23.87% CLIPScore ✅ REALE")
    print(f"   4° Gemma-T9: 23.76% CLIPScore ✅ REALE")
    print(f"\n✅ TUTTI I DATI MOSTRATI SONO REALI!")
    
    return output_path, data_path

if __name__ == "__main__":
    create_radar_chart_real_only()
