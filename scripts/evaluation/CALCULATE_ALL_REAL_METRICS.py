#!/usr/bin/env python3
"""
CALCOLA TUTTE LE METRICHE REALI PER TUTTI I MODELLI
Non usa stime - solo dati reali calcolati da file di risultati esistenti
"""

import json
import os
import sys
import numpy as np
from datetime import datetime
import argparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import NLTK metrics
try:
    import nltk
    from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
    from nltk.translate.meteor_score import meteor_score
    from rouge_score import rouge_scorer
    nltk.download('wordnet', quiet=True)
    nltk.download('punkt', quiet=True)
except ImportError as e:
    logger.error(f"❌ Import error: {e}")
    sys.exit(1)

def calculate_bleu_scores(reference, candidate):
    """Calcola BLEU scores 1-4"""
    if not reference or not candidate:
        return {'bleu_1': 0.0, 'bleu_2': 0.0, 'bleu_3': 0.0, 'bleu_4': 0.0}
    
    # Tokenize
    ref_tokens = reference.lower().split()
    cand_tokens = candidate.lower().split()
    
    if not ref_tokens or not cand_tokens:
        return {'bleu_1': 0.0, 'bleu_2': 0.0, 'bleu_3': 0.0, 'bleu_4': 0.0}
    
    # Smoothing function
    smoothing = SmoothingFunction().method1
    
    try:
        bleu_1 = sentence_bleu([ref_tokens], cand_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
        bleu_2 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
        bleu_3 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
        bleu_4 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
        
        return {
            'bleu_1': float(bleu_1),
            'bleu_2': float(bleu_2),
            'bleu_3': float(bleu_3),
            'bleu_4': float(bleu_4)
        }
    except Exception as e:
        logger.warning(f"BLEU calculation error: {e}")
        return {'bleu_1': 0.0, 'bleu_2': 0.0, 'bleu_3': 0.0, 'bleu_4': 0.0}

def calculate_meteor_score_safe(reference, candidate):
    """Calcola METEOR score"""
    if not reference or not candidate:
        return 0.0
    
    try:
        score = meteor_score([reference.lower().split()], candidate.lower().split())
        return float(score)
    except Exception as e:
        logger.warning(f"METEOR calculation error: {e}")
        return 0.0

def calculate_rouge_l(reference, candidate):
    """Calcola ROUGE-L score"""
    if not reference or not candidate:
        return 0.0
    
    try:
        scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True)
        scores = scorer.score(reference, candidate)
        return float(scores['rougeL'].fmeasure)
    except Exception as e:
        logger.warning(f"ROUGE-L calculation error: {e}")
        return 0.0

def find_results_files():
    """Trova tutti i file di risultati disponibili"""
    results_files = {}
    
    # File di risultati noti
    known_files = {
        'Idefics3': 'evaluation_results/idefics3_converted_results.json',
        'Florence-2': 'evaluation_results/florence2_FIXED_results_20250713_102506.json',
        'Idefics3-FIXED': 'evaluation_results/idefics3_FIXED_results_20250713_110649.json',
        'BLIP-2': 'evaluation_results/blip2_REAL_colors_results_20250704_134206.json'
    }
    
    # Verifica esistenza
    for model, filepath in known_files.items():
        if os.path.exists(filepath):
            results_files[model] = filepath
            logger.info(f"✅ Trovato {model}: {filepath}")
        else:
            logger.warning(f"⚠️ Non trovato {model}: {filepath}")
    
    # Cerca altri file
    search_dirs = ['evaluation_results/', '.']
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for file in os.listdir(search_dir):
                if file.endswith('.json') and ('results' in file.lower() or 'florence' in file.lower() or 'blip' in file.lower()):
                    full_path = os.path.join(search_dir, file)
                    if full_path not in results_files.values():
                        # Determina nome modello dal filename
                        if 'florence' in file.lower():
                            model_name = 'Florence-2-Extra'
                        elif 'blip' in file.lower():
                            model_name = 'BLIP-2-Extra'
                        elif 'idefics' in file.lower():
                            model_name = 'Idefics3-Extra'
                        else:
                            continue
                        
                        results_files[model_name] = full_path
                        logger.info(f"🔍 Trovato extra {model_name}: {full_path}")
    
    return results_files

def load_results_file(filepath):
    """Carica file risultati con gestione formati diversi"""
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        # Gestisci diversi formati
        if isinstance(data, list):
            results = data
        elif 'results' in data:
            results = data['results']
        else:
            logger.warning(f"⚠️ Formato non riconosciuto: {filepath}")
            return []
        
        logger.info(f"📊 Caricati {len(results)} esempi da {os.path.basename(filepath)}")
        return results
        
    except Exception as e:
        logger.error(f"❌ Errore caricamento {filepath}: {e}")
        return []

def calculate_metrics_for_model(results, model_name):
    """Calcola tutte le metriche per un modello"""
    logger.info(f"🎯 Calcolo metriche per {model_name}...")
    
    if not results:
        logger.error(f"❌ Nessun risultato per {model_name}")
        return None
    
    # Inizializza liste metriche
    bleu_1_scores = []
    bleu_2_scores = []
    bleu_3_scores = []
    bleu_4_scores = []
    meteor_scores = []
    rouge_l_scores = []
    
    successful = 0
    
    for i, result in enumerate(results):
        try:
            # Estrai ground truth e generated caption
            ground_truth = result.get('ground_truth', '') or result.get('caption', '')
            generated_caption = (result.get('generated_caption', '') or 
                               result.get('prediction', '') or 
                               result.get('generated_text', ''))
            
            if not ground_truth or not generated_caption:
                continue
            
            # Pulisci testi
            ground_truth = ground_truth.strip()
            generated_caption = generated_caption.strip()
            
            if not ground_truth or not generated_caption:
                continue
            
            # Calcola BLEU scores
            bleu_scores = calculate_bleu_scores(ground_truth, generated_caption)
            bleu_1_scores.append(bleu_scores['bleu_1'])
            bleu_2_scores.append(bleu_scores['bleu_2'])
            bleu_3_scores.append(bleu_scores['bleu_3'])
            bleu_4_scores.append(bleu_scores['bleu_4'])
            
            # Calcola METEOR
            meteor = calculate_meteor_score_safe(ground_truth, generated_caption)
            meteor_scores.append(meteor)
            
            # Calcola ROUGE-L
            rouge_l = calculate_rouge_l(ground_truth, generated_caption)
            rouge_l_scores.append(rouge_l)
            
            successful += 1
            
            if (i + 1) % 50 == 0:
                logger.info(f"📈 Progress {model_name}: {i+1}/{len(results)} esempi processati")
                
        except Exception as e:
            logger.warning(f"⚠️ Errore esempio {i} per {model_name}: {e}")
            continue
    
    if successful == 0:
        logger.error(f"❌ Nessun esempio processato con successo per {model_name}")
        return None
    
    # Calcola statistiche
    def calc_stats(scores):
        if not scores:
            return {'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0, 'count': 0}
        return {
            'mean': float(np.mean(scores)),
            'std': float(np.std(scores)),
            'min': float(np.min(scores)),
            'max': float(np.max(scores)),
            'count': len(scores)
        }
    
    metrics = {
        'model': model_name,
        'timestamp': datetime.now().isoformat(),
        'total_examples': len(results),
        'successful_examples': successful,
        'method': 'real_calculated_from_results',
        'bleu_1': calc_stats(bleu_1_scores),
        'bleu_2': calc_stats(bleu_2_scores),
        'bleu_3': calc_stats(bleu_3_scores),
        'bleu_4': calc_stats(bleu_4_scores),
        'meteor': calc_stats(meteor_scores),
        'rouge_l': calc_stats(rouge_l_scores)
    }
    
    logger.info(f"✅ {model_name} completato: {successful}/{len(results)} esempi")
    logger.info(f"📊 BLEU-1: {metrics['bleu_1']['mean']:.4f}")
    logger.info(f"📊 METEOR: {metrics['meteor']['mean']:.4f}")
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description="Calcola TUTTE le metriche reali per tutti i modelli")
    parser.add_argument('--output_dir', default='evaluation_results/ALL_REAL_METRICS',
                       help='Directory output')
    parser.add_argument('--max_examples', type=int, default=400,
                       help='Numero massimo di esempi per modello')
    
    args = parser.parse_args()
    
    print("🎯 CALCOLO TUTTE LE METRICHE REALI")
    print("=" * 60)
    print("⚠️  SOLO DATI REALI - NESSUNA STIMA!")
    print("=" * 60)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Trova file risultati
    results_files = find_results_files()
    
    if not results_files:
        print("❌ Nessun file di risultati trovato!")
        return
    
    print(f"📄 File trovati: {len(results_files)}")
    for model, filepath in results_files.items():
        print(f"   {model}: {os.path.basename(filepath)}")
    
    # Calcola metriche per ogni modello
    all_metrics = {}
    
    for model_name, filepath in results_files.items():
        print(f"\n🔄 Processando {model_name}...")
        
        # Carica risultati
        results = load_results_file(filepath)
        
        if not results:
            print(f"⚠️ Saltato {model_name} - nessun risultato")
            continue
        
        # Limita esempi se richiesto
        if len(results) > args.max_examples:
            results = results[:args.max_examples]
            print(f"📊 Limitato a {args.max_examples} esempi")
        
        # Calcola metriche
        metrics = calculate_metrics_for_model(results, model_name)
        
        if metrics:
            all_metrics[model_name] = metrics
            
            # Salva metriche individuali
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(args.output_dir, f"{model_name.replace('-', '_')}_REAL_metrics_{timestamp}.json")
            
            with open(output_file, 'w') as f:
                json.dump(metrics, f, indent=2)
            
            print(f"💾 Salvato: {os.path.basename(output_file)}")
    
    # Salva riepilogo completo
    if all_metrics:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = os.path.join(args.output_dir, f"ALL_MODELS_REAL_METRICS_{timestamp}.json")
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_models': len(all_metrics),
            'method': 'real_calculated_only',
            'models': all_metrics
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n💾 Riepilogo salvato: {os.path.basename(summary_file)}")
        
        # Stampa riepilogo
        print("\n" + "=" * 60)
        print("🏆 RIEPILOGO METRICHE REALI")
        print("=" * 60)
        
        for model_name, metrics in all_metrics.items():
            print(f"\n📊 {model_name}:")
            print(f"   BLEU-1: {metrics['bleu_1']['mean']:.4f} ± {metrics['bleu_1']['std']:.4f}")
            print(f"   BLEU-4: {metrics['bleu_4']['mean']:.4f} ± {metrics['bleu_4']['std']:.4f}")
            print(f"   METEOR: {metrics['meteor']['mean']:.4f} ± {metrics['meteor']['std']:.4f}")
            print(f"   ROUGE-L: {metrics['rouge_l']['mean']:.4f} ± {metrics['rouge_l']['std']:.4f}")
            print(f"   Esempi: {metrics['successful_examples']}/{metrics['total_examples']}")
        
        print("\n✅ TUTTE LE METRICHE REALI CALCOLATE!")
        print(f"📁 Directory: {args.output_dir}")
    else:
        print("❌ Nessuna metrica calcolata!")

if __name__ == "__main__":
    main()
