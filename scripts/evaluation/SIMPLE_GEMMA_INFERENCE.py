#!/usr/bin/env python3
"""
Inference semplice per Gemma - SOLO generazione caption
"""

import os
import json
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datetime import datetime
import argparse
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_gemma_model(checkpoint_path):
    """Carica modello Gemma"""
    logger.info(f"🔄 Caricando Gemma da: {checkpoint_path}")
    
    # Trova il modello base
    base_model = "google/gemma-2-9b-it"
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(base_model)
        model = AutoModelForCausalLM.from_pretrained(
            checkpoint_path,
            torch_dtype=torch.float16,
            device_map="cpu",
            trust_remote_code=True
        )
        
        logger.info("✅ Modello Gemma caricato")
        return tokenizer, model
        
    except Exception as e:
        logger.error(f"❌ Errore caricamento Gemma: {e}")
        return None, None

def generate_caption(tokenizer, model, xml_content):
    """Genera caption da XML"""
    try:
        # Prompt template
        prompt = f"""<bos><start_of_turn>user
Describe this SVG image in detail:

{xml_content}
<end_of_turn>
<start_of_turn>model
"""
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1024)
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=150,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Decode
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Estrai solo la risposta del modello
        if "<start_of_turn>model" in generated_text:
            caption = generated_text.split("<start_of_turn>model")[-1].strip()
        else:
            caption = generated_text.strip()
        
        return caption
        
    except Exception as e:
        logger.error(f"❌ Errore generazione: {e}")
        return f"ERROR: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="Inference semplice Gemma")
    parser.add_argument("--checkpoint", required=True, help="Path checkpoint Gemma")
    parser.add_argument("--dataset", required=True, help="Path dataset JSON")
    parser.add_argument("--output_file", required=True, help="File output")
    parser.add_argument("--max_examples", type=int, default=50, help="Max esempi")
    
    args = parser.parse_args()
    
    logger.info("🚀 INFERENCE GEMMA SEMPLICE")
    logger.info(f"📁 Checkpoint: {args.checkpoint}")
    logger.info(f"📊 Dataset: {args.dataset}")
    logger.info(f"📄 Output: {args.output_file}")
    logger.info(f"🔢 Max esempi: {args.max_examples}")
    
    # Carica modello
    tokenizer, model = load_gemma_model(args.checkpoint)
    if not tokenizer or not model:
        logger.error("❌ Impossibile caricare modello")
        return
    
    # Carica dataset
    with open(args.dataset, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(dataset)} esempi")
    
    # Limita esempi
    if len(dataset) > args.max_examples:
        dataset = dataset[:args.max_examples]
        logger.info(f"📊 Limitato a {args.max_examples} esempi")
    
    # Crea directory output
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Inference
    results = []
    successful = 0
    
    for i, example in enumerate(dataset):
        logger.info(f"🔄 Processando esempio {i+1}/{len(dataset)}")
        
        try:
            xml_content = example.get('xml_content', '')
            ground_truth = example.get('caption', '')
            example_id = example.get('id', i)
            
            # Genera caption
            generated_caption = generate_caption(tokenizer, model, xml_content)
            
            result = {
                'id': example_id,
                'xml_content': xml_content,
                'ground_truth': ground_truth,
                'generated_caption': generated_caption,
                'model': 'Gemma-T9'
            }
            
            results.append(result)
            
            if not generated_caption.startswith('ERROR'):
                successful += 1
            
            # Log progress
            if (i + 1) % 10 == 0:
                logger.info(f"📈 Progress: {i+1}/{len(dataset)} - Successi: {successful}")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            continue
    
    # Salva risultati
    output_data = {
        'model': 'Gemma-T9',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'checkpoint': args.checkpoint,
        'total_examples': len(dataset),
        'successful_examples': successful,
        'results': results
    }
    
    with open(args.output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    logger.info("=" * 50)
    logger.info("✅ INFERENCE COMPLETATA!")
    logger.info(f"📊 Esempi processati: {len(results)}")
    logger.info(f"✅ Successi: {successful}/{len(dataset)}")
    logger.info(f"📄 Risultati salvati: {args.output_file}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
