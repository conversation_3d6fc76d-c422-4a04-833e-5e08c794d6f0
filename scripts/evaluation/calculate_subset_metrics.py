#!/usr/bin/env python3
"""
Calculate metrics on subset of BLIP-1 results
"""

import json
import os
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
from rouge_score import rouge_scorer
import nltk

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

def calculate_bleu_scores(reference, candidate):
    """Calculate BLEU-1, BLEU-2, BLEU-3, BLEU-4"""
    ref_tokens = reference.lower().split()
    cand_tokens = candidate.lower().split()
    
    smoothing = SmoothingFunction().method1
    
    bleu1 = sentence_bleu([ref_tokens], cand_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
    bleu2 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
    bleu3 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
    bleu4 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
    
    return bleu1, bleu2, bleu3, bleu4

def calculate_meteor(reference, candidate):
    """Calculate METEOR score"""
    try:
        return meteor_score([reference.lower().split()], candidate.lower().split())
    except:
        return 0.0

def calculate_rouge_l(reference, candidate):
    """Calculate ROUGE-L score"""
    scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True)
    scores = scorer.score(reference, candidate)
    return scores['rougeL'].fmeasure

def main():
    # Load BLIP-1 results
    results_file = "evaluation_results/cpu_baseline_inference/blip1_minimal_20250730_154910.json"
    
    if not os.path.exists(results_file):
        print(f"❌ File not found: {results_file}")
        return
    
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    print(f"📊 Calculating metrics for {len(results)} BLIP-1 results...")
    
    # Calculate metrics
    bleu1_scores = []
    bleu2_scores = []
    bleu3_scores = []
    bleu4_scores = []
    meteor_scores = []
    rouge_l_scores = []
    
    valid_results = 0
    
    for result in results:
        if result['generated_caption'].startswith('ERROR'):
            continue
            
        reference = result['ground_truth']
        candidate = result['generated_caption']
        
        # BLEU scores
        bleu1, bleu2, bleu3, bleu4 = calculate_bleu_scores(reference, candidate)
        bleu1_scores.append(bleu1)
        bleu2_scores.append(bleu2)
        bleu3_scores.append(bleu3)
        bleu4_scores.append(bleu4)
        
        # METEOR
        meteor = calculate_meteor(reference, candidate)
        meteor_scores.append(meteor)
        
        # ROUGE-L
        rouge_l = calculate_rouge_l(reference, candidate)
        rouge_l_scores.append(rouge_l)
        
        valid_results += 1
    
    # Calculate averages
    metrics = {
        'model': 'BLIP-1',
        'dataset_size': len(results),
        'valid_results': valid_results,
        'BLEU-1': sum(bleu1_scores) / len(bleu1_scores) if bleu1_scores else 0,
        'BLEU-2': sum(bleu2_scores) / len(bleu2_scores) if bleu2_scores else 0,
        'BLEU-3': sum(bleu3_scores) / len(bleu3_scores) if bleu3_scores else 0,
        'BLEU-4': sum(bleu4_scores) / len(bleu4_scores) if bleu4_scores else 0,
        'METEOR': sum(meteor_scores) / len(meteor_scores) if meteor_scores else 0,
        'ROUGE-L': sum(rouge_l_scores) / len(rouge_l_scores) if rouge_l_scores else 0
    }
    
    print(f"\n🎯 BLIP-1 Metrics (subset of {valid_results} images):")
    print(f"BLEU-1:  {metrics['BLEU-1']:.4f}")
    print(f"BLEU-2:  {metrics['BLEU-2']:.4f}")
    print(f"BLEU-3:  {metrics['BLEU-3']:.4f}")
    print(f"BLEU-4:  {metrics['BLEU-4']:.4f}")
    print(f"METEOR:  {metrics['METEOR']:.4f}")
    print(f"ROUGE-L: {metrics['ROUGE-L']:.4f}")
    
    # Save metrics
    output_file = "evaluation_results/blip1_subset_metrics.json"
    with open(output_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    print(f"\n💾 Metrics saved to: {output_file}")
    
    # Show some examples
    print(f"\n📝 Sample results:")
    for i, result in enumerate(results[:3]):
        if not result['generated_caption'].startswith('ERROR'):
            print(f"\nExample {i+1}:")
            print(f"Ground truth: {result['ground_truth'][:100]}...")
            print(f"Generated:    {result['generated_caption']}")

if __name__ == "__main__":
    main()
