#!/usr/bin/env python3
"""
Inference semplice per Llama usando PEFT
"""

import os
import json
import torch
from datetime import datetime
import argparse
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_llama_with_peft(checkpoint_path):
    """Carica Llama con PEFT adapter"""
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        from peft import PeftModel
        
        logger.info(f"🔄 Caricando Llama da: {checkpoint_path}")
        
        # Modello base Llama
        base_model = "meta-llama/Llama-3.1-8B-Instruct"
        
        # Carica tokenizer
        tokenizer = AutoTokenizer.from_pretrained(base_model)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Carica modello base
        model = AutoModelForCausalLM.from_pretrained(
            base_model,
            torch_dtype=torch.float16,
            device_map="cpu",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # Carica adapter PEFT
        model = PeftModel.from_pretrained(model, checkpoint_path)
        
        logger.info("✅ Llama + PEFT caricato con successo")
        return tokenizer, model
        
    except Exception as e:
        logger.error(f"❌ Errore caricamento Llama: {e}")
        return None, None

def generate_caption_llama(tokenizer, model, xml_content):
    """Genera caption con Llama"""
    try:
        # Prompt Llama format
        prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

Describe this SVG image in detail:

{xml_content[:500]}

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=100,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        # Decode
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Estrai solo la risposta dell'assistant
        if "<|start_header_id|>assistant<|end_header_id|>" in generated_text:
            caption = generated_text.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
        else:
            caption = generated_text.strip()
        
        return caption if caption else "Generated caption"
        
    except Exception as e:
        logger.error(f"❌ Errore generazione: {e}")
        return f"ERROR: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="Inference Llama semplice")
    parser.add_argument("--checkpoint", required=True, help="Path checkpoint PEFT")
    parser.add_argument("--dataset", required=True, help="Path dataset")
    parser.add_argument("--output_file", required=True, help="File output")
    parser.add_argument("--max_examples", type=int, default=20, help="Max esempi")
    
    args = parser.parse_args()
    
    logger.info("🚀 INFERENCE LLAMA SEMPLICE")
    logger.info(f"📁 Checkpoint: {args.checkpoint}")
    logger.info(f"📊 Dataset: {args.dataset}")
    logger.info(f"📄 Output: {args.output_file}")
    logger.info(f"🔢 Max esempi: {args.max_examples}")
    
    # Carica modello
    tokenizer, model = load_llama_with_peft(args.checkpoint)
    if not tokenizer or not model:
        logger.error("❌ Impossibile caricare modello")
        return
    
    # Carica dataset
    with open(args.dataset, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(dataset)} esempi")
    
    # Limita esempi
    if len(dataset) > args.max_examples:
        dataset = dataset[:args.max_examples]
        logger.info(f"📊 Limitato a {args.max_examples} esempi")
    
    # Crea directory output
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Inference
    results = []
    successful = 0
    
    for i, example in enumerate(dataset):
        logger.info(f"🔄 Processando esempio {i+1}/{len(dataset)}")
        
        try:
            xml_content = example.get('xml_content', '')
            ground_truth = example.get('caption', '')
            example_id = example.get('id', i)
            
            # Genera caption
            generated_caption = generate_caption_llama(tokenizer, model, xml_content)
            
            result = {
                'id': example_id,
                'xml_content': xml_content,
                'ground_truth': ground_truth,
                'generated_caption': generated_caption,
                'model': 'Llama-T8'
            }
            
            results.append(result)
            
            if not generated_caption.startswith('ERROR'):
                successful += 1
            
            logger.info(f"✅ Esempio {i+1}: {generated_caption[:50]}...")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            continue
    
    # Salva risultati
    output_data = {
        'model': 'Llama-T8',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'checkpoint': args.checkpoint,
        'total_examples': len(dataset),
        'successful_examples': successful,
        'results': results
    }
    
    with open(args.output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    logger.info("=" * 50)
    logger.info("✅ INFERENCE COMPLETATA!")
    logger.info(f"📊 Esempi processati: {len(results)}")
    logger.info(f"✅ Successi: {successful}/{len(dataset)}")
    logger.info(f"📄 Risultati salvati: {args.output_file}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
