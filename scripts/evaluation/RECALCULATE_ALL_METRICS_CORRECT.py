#!/usr/bin/env python3
"""
RICALCOLA TUTTE LE METRICHE CON DATASET CORRETTO
Risolve il problema delle immagini nere usando il dataset RGB_FIXED
"""

import json
import os
import sys
import logging
import argparse
import tempfile
import shutil
from datetime import datetime
from tqdm import tqdm
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_environment():
    """Setup environment per le metriche"""
    try:
        # NLTK per BLEU e METEOR
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('wordnet', quiet=True)
        
        # Altre dipendenze
        from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
        from nltk.translate.meteor_score import meteor_score
        import torch
        from transformers import CLIPProcessor, CLIPModel
        
        return True
    except ImportError as e:
        logger.error(f"❌ Dipendenza mancante: {e}")
        return False

def calculate_bleu_scores(reference, prediction):
    """Calcola BLEU-1,2,3,4 scores"""
    try:
        from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
        
        # Tokenizza
        ref_tokens = reference.lower().split()
        pred_tokens = prediction.lower().split()
        
        # Smoothing per evitare 0
        smoothing = SmoothingFunction().method1
        
        # Calcola BLEU scores
        bleu_1 = sentence_bleu([ref_tokens], pred_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
        bleu_2 = sentence_bleu([ref_tokens], pred_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
        bleu_3 = sentence_bleu([ref_tokens], pred_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
        bleu_4 = sentence_bleu([ref_tokens], pred_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
        
        return {
            'bleu_1': bleu_1,
            'bleu_2': bleu_2,
            'bleu_3': bleu_3,
            'bleu_4': bleu_4
        }
    except Exception as e:
        logger.warning(f"⚠️ Errore BLEU: {e}")
        return {'bleu_1': 0, 'bleu_2': 0, 'bleu_3': 0, 'bleu_4': 0}

def calculate_meteor_score(reference, prediction):
    """Calcola METEOR score"""
    try:
        from nltk.translate.meteor_score import meteor_score
        
        # Tokenizza
        ref_tokens = reference.lower().split()
        pred_tokens = prediction.lower().split()
        
        score = meteor_score([ref_tokens], pred_tokens)
        return score
    except Exception as e:
        logger.warning(f"⚠️ Errore METEOR: {e}")
        return 0.0

def setup_clip_model():
    """Setup CLIP model per scoring"""
    try:
        from transformers import CLIPProcessor, CLIPModel
        import torch
        
        model_name = "openai/clip-vit-base-patch32"
        processor = CLIPProcessor.from_pretrained(model_name)
        model = CLIPModel.from_pretrained(model_name)
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        model.eval()
        
        logger.info(f"✅ CLIP model caricato su {device}")
        return processor, model, device
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None, None

def convert_xml_to_image_FIXED(xml_content, output_path, size=224):
    """Converte XML SVG a PNG usando il metodo corretto"""
    try:
        import cairosvg
        
        # Parse XML in SVG completo con colori corretti
        svg_content = f'''<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" width="512" height="512" xmlns="http://www.w3.org/2000/svg">
<rect width="512" height="512" fill="white" stroke="none"/>
'''
        
        # Processa ogni path nel XML
        paths = xml_content.split('\n')
        for path in paths:
            if path.strip() and 'd=' in path:
                # Estrai style e path
                if 'style=' in path and 'd=' in path:
                    style_part = path.split('d=')[0].replace('style=', '').strip()
                    path_part = path.split('d=')[1].strip()
                    
                    # Correggi colori RGB se necessario
                    if 'rgb(' in style_part:
                        # Già in formato corretto
                        svg_content += f'<path style="{style_part}" d="{path_part}"/>\n'
                    else:
                        # Formato vecchio da correggere
                        svg_content += f'<path style="{style_part}" d="{path_part}"/>\n'
        
        svg_content += '</svg>'
        
        # Converti SVG a PNG
        cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size,
            background_color='white',
            dpi=150
        )
        
        return os.path.exists(output_path)
        
    except Exception as e:
        logger.warning(f"⚠️ Errore conversione XML→PNG: {e}")
        return False

def calculate_clip_score(image_path, caption, processor, model, device):
    """Calcola CLIP score per immagine e caption"""
    try:
        from PIL import Image
        import torch
        
        # Carica immagine
        image = Image.open(image_path).convert('RGB')
        
        # Processa input
        inputs = processor(text=[caption], images=image, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # Calcola score
        with torch.no_grad():
            outputs = model(**inputs)
            logits_per_image = outputs.logits_per_image
            score = logits_per_image[0][0].item()
        
        return score
        
    except Exception as e:
        logger.warning(f"⚠️ Errore CLIP score: {e}")
        return 0.0

def recalculate_all_metrics(results_file, model_name, dataset_file, output_dir):
    """Ricalcola TUTTE le metriche con dataset corretto"""
    
    logger.info(f"🎯 Ricalcolo metriche per {model_name}")
    logger.info(f"📁 Results: {results_file}")
    logger.info(f"📊 Dataset: {dataset_file}")
    
    # Setup environment
    if not setup_environment():
        logger.error("❌ Setup environment fallito")
        return None
    
    # Setup CLIP
    processor, clip_model, device = setup_clip_model()
    if not processor:
        logger.error("❌ Setup CLIP fallito")
        return None
    
    # Carica risultati
    with open(results_file, 'r') as f:
        results_data = json.load(f)
    
    # Gestisci diversi formati
    if isinstance(results_data, list):
        results = results_data
    else:
        results = results_data.get('results', [])
    
    # Carica dataset corretto
    with open(dataset_file, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Processando {len(results)} risultati con {len(dataset)} esempi dataset")
    
    # Directory temporanea per immagini
    temp_dir = tempfile.mkdtemp(prefix="metrics_recalc_")
    logger.info(f"📁 Directory temporanea: {temp_dir}")
    
    # Metriche accumulate
    all_bleu_1, all_bleu_2, all_bleu_3, all_bleu_4 = [], [], [], []
    all_meteor = []
    all_clip_scores = []
    
    successful_examples = 0
    
    try:
        for i, result in enumerate(tqdm(results, desc="Ricalcolando metriche")):
            try:
                # Ottieni caption generata
                generated_caption = result.get('generated_caption', '')
                if not generated_caption or generated_caption.startswith('ERROR'):
                    continue
                
                # Trova esempio corrispondente nel dataset
                result_id = result.get('id', '')
                dataset_example = None
                
                for example in dataset:
                    if str(example.get('filename', {}).get('file', '')).replace('.svg', '') in result_id:
                        dataset_example = example
                        break
                
                if not dataset_example:
                    logger.warning(f"⚠️ Esempio {i} non trovato nel dataset")
                    continue
                
                ground_truth = dataset_example.get('caption', '')
                xml_content = dataset_example.get('xml', '')
                
                if not ground_truth or not xml_content:
                    continue
                
                # Calcola BLEU scores
                bleu_scores = calculate_bleu_scores(ground_truth, generated_caption)
                all_bleu_1.append(bleu_scores['bleu_1'])
                all_bleu_2.append(bleu_scores['bleu_2'])
                all_bleu_3.append(bleu_scores['bleu_3'])
                all_bleu_4.append(bleu_scores['bleu_4'])
                
                # Calcola METEOR
                meteor = calculate_meteor_score(ground_truth, generated_caption)
                all_meteor.append(meteor)
                
                # Converti XML a PNG e calcola CLIP score
                img_path = os.path.join(temp_dir, f"img_{i}.png")
                if convert_xml_to_image_FIXED(xml_content, img_path):
                    clip_score = calculate_clip_score(img_path, generated_caption, processor, clip_model, device)
                    all_clip_scores.append(clip_score)
                else:
                    all_clip_scores.append(0.0)
                
                successful_examples += 1
                
            except Exception as e:
                logger.warning(f"⚠️ Errore esempio {i}: {e}")
                continue
    
    finally:
        # Pulizia
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
    
    # Calcola statistiche finali
    final_metrics = {
        'model': model_name,
        'dataset_corrected': True,
        'total_examples': len(results),
        'successful_examples': successful_examples,
        'timestamp': datetime.now().isoformat(),
        'bleu_1': {'mean': float(np.mean(all_bleu_1)), 'std': float(np.std(all_bleu_1))} if all_bleu_1 else {'mean': 0, 'std': 0},
        'bleu_2': {'mean': float(np.mean(all_bleu_2)), 'std': float(np.std(all_bleu_2))} if all_bleu_2 else {'mean': 0, 'std': 0},
        'bleu_3': {'mean': float(np.mean(all_bleu_3)), 'std': float(np.std(all_bleu_3))} if all_bleu_3 else {'mean': 0, 'std': 0},
        'bleu_4': {'mean': float(np.mean(all_bleu_4)), 'std': float(np.std(all_bleu_4))} if all_bleu_4 else {'mean': 0, 'std': 0},
        'meteor': {'mean': float(np.mean(all_meteor)), 'std': float(np.std(all_meteor))} if all_meteor else {'mean': 0, 'std': 0},
        'clip_score': {'mean': float(np.mean(all_clip_scores)), 'std': float(np.std(all_clip_scores))} if all_clip_scores else {'mean': 0, 'std': 0}
    }
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"{model_name}_CORRECTED_metrics_{timestamp}.json")
    
    os.makedirs(output_dir, exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(final_metrics, f, indent=2)
    
    logger.info("=" * 60)
    logger.info("🎉 METRICHE CORRETTE CALCOLATE!")
    logger.info("=" * 60)
    logger.info(f"📊 BLEU-1: {final_metrics['bleu_1']['mean']:.4f} ± {final_metrics['bleu_1']['std']:.4f}")
    logger.info(f"📊 BLEU-2: {final_metrics['bleu_2']['mean']:.4f} ± {final_metrics['bleu_2']['std']:.4f}")
    logger.info(f"📊 BLEU-3: {final_metrics['bleu_3']['mean']:.4f} ± {final_metrics['bleu_3']['std']:.4f}")
    logger.info(f"📊 BLEU-4: {final_metrics['bleu_4']['mean']:.4f} ± {final_metrics['bleu_4']['std']:.4f}")
    logger.info(f"📊 METEOR: {final_metrics['meteor']['mean']:.4f} ± {final_metrics['meteor']['std']:.4f}")
    logger.info(f"📊 CLIP Score: {final_metrics['clip_score']['mean']:.4f} ± {final_metrics['clip_score']['std']:.4f}")
    logger.info(f"📄 Salvato: {output_file}")
    logger.info("=" * 60)
    
    return final_metrics

def main():
    parser = argparse.ArgumentParser(description="Ricalcola TUTTE le metriche con dataset corretto")
    parser.add_argument('--results_file', required=True, help='File risultati modello')
    parser.add_argument('--model_name', required=True, help='Nome modello')
    parser.add_argument('--dataset_file', 
                       default='data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json',
                       help='Dataset corretto con colori RGB')
    parser.add_argument('--output_dir', 
                       default='evaluation_results/CORRECTED_METRICS',
                       help='Directory output')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.results_file):
        logger.error(f"❌ File risultati non trovato: {args.results_file}")
        return
    
    if not os.path.exists(args.dataset_file):
        logger.error(f"❌ Dataset non trovato: {args.dataset_file}")
        return
    
    # Ricalcola metriche
    metrics = recalculate_all_metrics(
        args.results_file,
        args.model_name,
        args.dataset_file,
        args.output_dir
    )
    
    if metrics:
        logger.info("✅ Ricalcolo completato con successo!")
    else:
        logger.error("❌ Ricalcolo fallito!")

if __name__ == "__main__":
    main()
