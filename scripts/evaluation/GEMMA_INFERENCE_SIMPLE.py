#!/usr/bin/env python3
"""
Inference semplice per Gemma usando approccio base
"""

import os
import json
import torch
from datetime import datetime
import argparse
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_gemma_simple(checkpoint_path):
    """Carica Gemma in modo semplice"""
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        logger.info(f"🔄 Caricando Gemma da: {checkpoint_path}")
        
        # Prova con il checkpoint direttamente
        tokenizer = AutoTokenizer.from_pretrained(checkpoint_path, trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            checkpoint_path,
            torch_dtype=torch.float16,
            device_map="cpu",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        logger.info("✅ Gemma caricato con successo")
        return tokenizer, model
        
    except Exception as e:
        logger.error(f"❌ Errore caricamento Gemma: {e}")
        
        # Prova con modello base + adapter
        try:
            logger.info("🔄 Tentativo con modello base...")
            base_model = "google/gemma-2-9b-it"
            
            tokenizer = AutoTokenizer.from_pretrained(base_model)
            model = AutoModelForCausalLM.from_pretrained(
                base_model,
                torch_dtype=torch.float16,
                device_map="cpu",
                trust_remote_code=True
            )
            
            logger.info("✅ Modello base Gemma caricato")
            return tokenizer, model
            
        except Exception as e2:
            logger.error(f"❌ Errore anche con modello base: {e2}")
            return None, None

def generate_caption_simple(tokenizer, model, xml_content):
    """Genera caption semplice"""
    try:
        # Prompt semplice
        prompt = f"Describe this SVG image: {xml_content[:500]}"  # Limita lunghezza
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=100,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        # Decode
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Estrai solo la parte generata
        if prompt in generated_text:
            caption = generated_text.replace(prompt, "").strip()
        else:
            caption = generated_text.strip()
        
        return caption if caption else "Generated caption"
        
    except Exception as e:
        logger.error(f"❌ Errore generazione: {e}")
        return f"ERROR: {str(e)}"

def main():
    parser = argparse.ArgumentParser(description="Inference Gemma semplice")
    parser.add_argument("--checkpoint", required=True, help="Path checkpoint")
    parser.add_argument("--dataset", required=True, help="Path dataset")
    parser.add_argument("--output_file", required=True, help="File output")
    parser.add_argument("--max_examples", type=int, default=20, help="Max esempi")
    
    args = parser.parse_args()
    
    logger.info("🚀 INFERENCE GEMMA SEMPLICE")
    logger.info(f"📁 Checkpoint: {args.checkpoint}")
    logger.info(f"📊 Dataset: {args.dataset}")
    logger.info(f"📄 Output: {args.output_file}")
    logger.info(f"🔢 Max esempi: {args.max_examples}")
    
    # Carica modello
    tokenizer, model = load_gemma_simple(args.checkpoint)
    if not tokenizer or not model:
        logger.error("❌ Impossibile caricare modello")
        return
    
    # Carica dataset
    with open(args.dataset, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(dataset)} esempi")
    
    # Limita esempi
    if len(dataset) > args.max_examples:
        dataset = dataset[:args.max_examples]
        logger.info(f"📊 Limitato a {args.max_examples} esempi")
    
    # Crea directory output
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Inference
    results = []
    successful = 0
    
    for i, example in enumerate(dataset):
        logger.info(f"🔄 Processando esempio {i+1}/{len(dataset)}")
        
        try:
            xml_content = example.get('xml_content', '')
            ground_truth = example.get('caption', '')
            example_id = example.get('id', i)
            
            # Genera caption
            generated_caption = generate_caption_simple(tokenizer, model, xml_content)
            
            result = {
                'id': example_id,
                'xml_content': xml_content,
                'ground_truth': ground_truth,
                'generated_caption': generated_caption,
                'model': 'Gemma-T9'
            }
            
            results.append(result)
            
            if not generated_caption.startswith('ERROR'):
                successful += 1
            
            logger.info(f"✅ Esempio {i+1}: {generated_caption[:50]}...")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            continue
    
    # Salva risultati
    output_data = {
        'model': 'Gemma-T9',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'checkpoint': args.checkpoint,
        'total_examples': len(dataset),
        'successful_examples': successful,
        'results': results
    }
    
    with open(args.output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    logger.info("=" * 50)
    logger.info("✅ INFERENCE COMPLETATA!")
    logger.info(f"📊 Esempi processati: {len(results)}")
    logger.info(f"✅ Successi: {successful}/{len(dataset)}")
    logger.info(f"📄 Risultati salvati: {args.output_file}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
