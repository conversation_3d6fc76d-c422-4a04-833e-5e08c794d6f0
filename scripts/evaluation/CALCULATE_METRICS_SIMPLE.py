#!/usr/bin/env python3
"""
Calcola metriche direttamente dai risultati esistenti
Semplice e veloce per ottenere BLEU, METEOR, CLIP scores
"""

import json
import os
import sys
import logging
import argparse
import numpy as np
from datetime import datetime
from tqdm import tqdm

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_nltk():
    """Setup NLTK"""
    try:
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('wordnet', quiet=True)
        return True
    except ImportError:
        logger.error("❌ NLTK non installato")
        return False

def calculate_bleu_scores(reference, prediction):
    """Calcola BLEU-1,2,3,4 scores"""
    try:
        from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
        
        # Tokenizza
        ref_tokens = reference.lower().split()
        pred_tokens = prediction.lower().split()
        
        if not ref_tokens or not pred_tokens:
            return {'bleu_1': 0, 'bleu_2': 0, 'bleu_3': 0, 'bleu_4': 0}
        
        # Smoothing per evitare 0
        smoothing = SmoothingFunction().method1
        
        # Calcola BLEU scores
        bleu_1 = sentence_bleu([ref_tokens], pred_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
        bleu_2 = sentence_bleu([ref_tokens], pred_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
        bleu_3 = sentence_bleu([ref_tokens], pred_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
        bleu_4 = sentence_bleu([ref_tokens], pred_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
        
        return {
            'bleu_1': bleu_1,
            'bleu_2': bleu_2,
            'bleu_3': bleu_3,
            'bleu_4': bleu_4
        }
    except Exception as e:
        logger.warning(f"⚠️ Errore BLEU: {e}")
        return {'bleu_1': 0, 'bleu_2': 0, 'bleu_3': 0, 'bleu_4': 0}

def calculate_meteor_score(reference, prediction):
    """Calcola METEOR score"""
    try:
        from nltk.translate.meteor_score import meteor_score
        
        # Tokenizza
        ref_tokens = reference.lower().split()
        pred_tokens = prediction.lower().split()
        
        if not ref_tokens or not pred_tokens:
            return 0.0
        
        score = meteor_score([ref_tokens], pred_tokens)
        return score
    except Exception as e:
        logger.warning(f"⚠️ Errore METEOR: {e}")
        return 0.0

def calculate_rouge_l(reference, prediction):
    """Calcola ROUGE-L score semplificato"""
    try:
        ref_tokens = set(reference.lower().split())
        pred_tokens = set(prediction.lower().split())
        
        if not ref_tokens or not pred_tokens:
            return 0.0
        
        # F1-score semplificato
        intersection = len(ref_tokens.intersection(pred_tokens))
        precision = intersection / len(pred_tokens) if pred_tokens else 0
        recall = intersection / len(ref_tokens) if ref_tokens else 0
        
        if precision + recall == 0:
            return 0.0
        
        f1 = 2 * (precision * recall) / (precision + recall)
        return f1
        
    except Exception as e:
        logger.warning(f"⚠️ Errore ROUGE-L: {e}")
        return 0.0

def setup_clip_model():
    """Setup CLIP model per scoring"""
    try:
        from transformers import CLIPProcessor, CLIPModel
        import torch
        
        model_name = "openai/clip-vit-base-patch32"
        processor = CLIPProcessor.from_pretrained(model_name)
        model = CLIPModel.from_pretrained(model_name)
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        model.eval()
        
        logger.info(f"✅ CLIP model caricato su {device}")
        return processor, model, device
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None, None

def calculate_clip_score_text_only(reference, prediction, processor, model, device):
    """Calcola CLIP score solo tra testi (senza immagine)"""
    try:
        import torch
        
        # Processa testi
        inputs = processor(text=[reference, prediction], return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # Calcola embeddings
        with torch.no_grad():
            text_features = model.get_text_features(**inputs)
            
            # Normalizza
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Similarità coseno
            similarity = torch.cosine_similarity(text_features[0:1], text_features[1:2], dim=-1)
            score = similarity.item()
        
        return score
        
    except Exception as e:
        logger.warning(f"⚠️ Errore CLIP score: {e}")
        return 0.0

def calculate_all_metrics_simple(results_file, model_name, output_dir):
    """Calcola tutte le metriche dai risultati esistenti"""
    
    logger.info(f"🎯 Calcolo metriche semplici per {model_name}")
    logger.info(f"📁 File: {results_file}")
    
    # Setup
    if not setup_nltk():
        return None
    
    # Setup CLIP
    processor, clip_model, device = setup_clip_model()
    if not processor:
        logger.warning("⚠️ CLIP non disponibile, salto CLIP scores")
    
    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    # Gestisci diversi formati
    if isinstance(data, list):
        results = data
    elif 'results' in data:
        results = data['results']
    else:
        logger.error("❌ Formato risultati non riconosciuto")
        return None
    
    logger.info(f"📊 Processando {len(results)} risultati")
    
    # Metriche accumulate
    all_bleu_1, all_bleu_2, all_bleu_3, all_bleu_4 = [], [], [], []
    all_meteor = []
    all_rouge_l = []
    all_clip_scores = []
    
    successful_examples = 0
    
    for i, result in enumerate(tqdm(results, desc="Calcolando metriche")):
        try:
            # Ottieni caption
            ground_truth = result.get('ground_truth', '')
            generated_caption = result.get('generated_caption', '')
            
            if not ground_truth or not generated_caption:
                continue
            
            if generated_caption.startswith('ERROR') or len(generated_caption.strip()) < 3:
                continue
            
            # Calcola BLEU scores
            bleu_scores = calculate_bleu_scores(ground_truth, generated_caption)
            all_bleu_1.append(bleu_scores['bleu_1'])
            all_bleu_2.append(bleu_scores['bleu_2'])
            all_bleu_3.append(bleu_scores['bleu_3'])
            all_bleu_4.append(bleu_scores['bleu_4'])
            
            # Calcola METEOR
            meteor = calculate_meteor_score(ground_truth, generated_caption)
            all_meteor.append(meteor)
            
            # Calcola ROUGE-L
            rouge_l = calculate_rouge_l(ground_truth, generated_caption)
            all_rouge_l.append(rouge_l)
            
            # Calcola CLIP score (text-only)
            if processor:
                clip_score = calculate_clip_score_text_only(ground_truth, generated_caption, processor, clip_model, device)
                all_clip_scores.append(clip_score)
            else:
                all_clip_scores.append(0.0)
            
            successful_examples += 1
            
        except Exception as e:
            logger.warning(f"⚠️ Errore esempio {i}: {e}")
            continue
    
    # Calcola statistiche finali
    final_metrics = {
        'model': model_name,
        'method': 'simple_text_based',
        'total_examples': len(results),
        'successful_examples': successful_examples,
        'timestamp': datetime.now().isoformat(),
        'bleu_1': {'mean': float(np.mean(all_bleu_1)), 'std': float(np.std(all_bleu_1))} if all_bleu_1 else {'mean': 0, 'std': 0},
        'bleu_2': {'mean': float(np.mean(all_bleu_2)), 'std': float(np.std(all_bleu_2))} if all_bleu_2 else {'mean': 0, 'std': 0},
        'bleu_3': {'mean': float(np.mean(all_bleu_3)), 'std': float(np.std(all_bleu_3))} if all_bleu_3 else {'mean': 0, 'std': 0},
        'bleu_4': {'mean': float(np.mean(all_bleu_4)), 'std': float(np.std(all_bleu_4))} if all_bleu_4 else {'mean': 0, 'std': 0},
        'meteor': {'mean': float(np.mean(all_meteor)), 'std': float(np.std(all_meteor))} if all_meteor else {'mean': 0, 'std': 0},
        'rouge_l': {'mean': float(np.mean(all_rouge_l)), 'std': float(np.std(all_rouge_l))} if all_rouge_l else {'mean': 0, 'std': 0},
        'clip_score_text': {'mean': float(np.mean(all_clip_scores)), 'std': float(np.std(all_clip_scores))} if all_clip_scores else {'mean': 0, 'std': 0}
    }
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"{model_name}_SIMPLE_metrics_{timestamp}.json")
    
    os.makedirs(output_dir, exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(final_metrics, f, indent=2)
    
    logger.info("=" * 60)
    logger.info("🎉 METRICHE SEMPLICI CALCOLATE!")
    logger.info("=" * 60)
    logger.info(f"📊 BLEU-1: {final_metrics['bleu_1']['mean']:.4f} ± {final_metrics['bleu_1']['std']:.4f}")
    logger.info(f"📊 BLEU-2: {final_metrics['bleu_2']['mean']:.4f} ± {final_metrics['bleu_2']['std']:.4f}")
    logger.info(f"📊 BLEU-3: {final_metrics['bleu_3']['mean']:.4f} ± {final_metrics['bleu_3']['std']:.4f}")
    logger.info(f"📊 BLEU-4: {final_metrics['bleu_4']['mean']:.4f} ± {final_metrics['bleu_4']['std']:.4f}")
    logger.info(f"📊 METEOR: {final_metrics['meteor']['mean']:.4f} ± {final_metrics['meteor']['std']:.4f}")
    logger.info(f"📊 ROUGE-L: {final_metrics['rouge_l']['mean']:.4f} ± {final_metrics['rouge_l']['std']:.4f}")
    logger.info(f"📊 CLIP Text: {final_metrics['clip_score_text']['mean']:.4f} ± {final_metrics['clip_score_text']['std']:.4f}")
    logger.info(f"📄 Salvato: {output_file}")
    logger.info("=" * 60)
    
    return final_metrics

def main():
    parser = argparse.ArgumentParser(description="Calcola metriche semplici dai risultati")
    parser.add_argument('--results_file', required=True, help='File risultati modello')
    parser.add_argument('--model_name', required=True, help='Nome modello')
    parser.add_argument('--output_dir', 
                       default='evaluation_results/SIMPLE_METRICS',
                       help='Directory output')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.results_file):
        logger.error(f"❌ File risultati non trovato: {args.results_file}")
        return
    
    # Calcola metriche
    metrics = calculate_all_metrics_simple(
        args.results_file,
        args.model_name,
        args.output_dir
    )
    
    if metrics:
        logger.info("✅ Calcolo completato con successo!")
    else:
        logger.error("❌ Calcolo fallito!")

if __name__ == "__main__":
    main()
