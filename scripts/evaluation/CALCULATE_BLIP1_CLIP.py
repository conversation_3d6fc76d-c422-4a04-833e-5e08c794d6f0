#!/usr/bin/env python3
"""
Calcola CLIPScore reale per BLIP-1
"""

import os
import json
import torch
import numpy as np
from datetime import datetime
import logging
from transformers import CLIPProcessor, CLIPModel
from PIL import Image

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_clip_score_baseline(image_path, text, model, processor, device):
    """Calcola CLIP score per un'immagine e testo"""
    try:
        # Carica immagine
        image = Image.open(image_path).convert('RGB')
        
        # Preprocessa
        inputs = processor(text=[text], images=[image], return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # Calcola logits
        with torch.no_grad():
            outputs = model(**inputs)
            logits_per_image = outputs.logits_per_image
            clip_score = logits_per_image[0, 0].item()
        
        return clip_score
        
    except Exception as e:
        logger.warning(f"Errore CLIP per {image_path}: {e}")
        return 0.0

def calculate_blip1_clip():
    """Calcola CLIPScore per BLIP-1"""
    
    # File BLIP-1
    blip1_file = "evaluation_results/cpu_baseline_inference/blip1_minimal_20250730_154910.json"
    
    if not os.path.exists(blip1_file):
        logger.error(f"File BLIP-1 non trovato: {blip1_file}")
        return
    
    # Carica modello CLIP
    logger.info("🔄 Caricando modello CLIP...")
    device = torch.device("cpu")
    model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32").to(device)
    processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
    logger.info("✅ Modello CLIP caricato")
    
    # Carica risultati
    with open(blip1_file, 'r') as f:
        results = json.load(f)
    
    logger.info(f"📊 Caricati {len(results)} risultati BLIP-1")
    
    # Calcola CLIPScore
    clip_scores = []
    successful = 0
    
    for i, result in enumerate(results):
        try:
            image_path = result.get('image_path', '')
            generated_caption = result.get('generated_caption', '')
            
            if not image_path or not generated_caption:
                continue
            
            if not os.path.exists(image_path):
                logger.warning(f"Immagine non trovata: {image_path}")
                continue
            
            # Calcola CLIP Score
            clip_score = calculate_clip_score_baseline(image_path, generated_caption, model, processor, device)
            clip_scores.append(clip_score)
            successful += 1
            
            if (i + 1) % 5 == 0:
                logger.info(f"📈 Progress: {i+1}/{len(results)} - CLIP medio: {np.mean(clip_scores):.4f}")
                
        except Exception as e:
            logger.warning(f"Errore esempio {i}: {e}")
            continue
    
    if not clip_scores:
        logger.error("❌ Nessun CLIPScore calcolato!")
        return
    
    # Statistiche
    clip_stats = {
        'model': 'BLIP-1',
        'timestamp': datetime.now().isoformat(),
        'total_examples': len(results),
        'successful_examples': successful,
        'clip_scores': clip_scores,
        'clip_score_mean': np.mean(clip_scores),
        'clip_score_std': np.std(clip_scores),
        'clip_score_min': np.min(clip_scores),
        'clip_score_max': np.max(clip_scores)
    }
    
    # Salva risultati
    output_dir = "evaluation_results/clip_scores"
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"blip1_BASELINE_CLIP_RAW_{timestamp}.json")
    
    with open(output_file, 'w') as f:
        json.dump(clip_stats, f, indent=2)
    
    # Report
    logger.info("=" * 60)
    logger.info("✅ CLIP SCORE BLIP-1 CALCOLATO!")
    logger.info("=" * 60)
    logger.info(f"📊 CLIP Score medio: {clip_stats['clip_score_mean']:.4f} ± {clip_stats['clip_score_std']:.4f}")
    logger.info(f"📊 Range: {clip_stats['clip_score_min']:.4f} - {clip_stats['clip_score_max']:.4f}")
    logger.info(f"📊 Esempi: {successful}/{len(results)}")
    logger.info(f"📄 Salvato: {output_file}")
    logger.info("=" * 60)

if __name__ == "__main__":
    calculate_blip1_clip()
