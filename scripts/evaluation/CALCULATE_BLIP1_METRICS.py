#!/usr/bin/env python3
"""
Calcola metriche reali per BLIP-1
"""

import os
import json
import numpy as np
from datetime import datetime
import logging
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
import nltk

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_bleu_scores(reference, candidate):
    """Calcola BLEU scores"""
    try:
        # Tokenize
        ref_tokens = reference.lower().split()
        cand_tokens = candidate.lower().split()
        
        if not ref_tokens or not cand_tokens:
            return {'bleu_1': 0.0, 'bleu_2': 0.0, 'bleu_3': 0.0, 'bleu_4': 0.0}
        
        # Smoothing function
        smoothing = SmoothingFunction().method1
        
        # Calculate BLEU scores
        bleu_1 = sentence_bleu([ref_tokens], cand_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
        bleu_2 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
        bleu_3 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
        bleu_4 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
        
        return {
            'bleu_1': bleu_1,
            'bleu_2': bleu_2,
            'bleu_3': bleu_3,
            'bleu_4': bleu_4
        }
        
    except Exception as e:
        logger.warning(f"Errore BLEU: {e}")
        return {'bleu_1': 0.0, 'bleu_2': 0.0, 'bleu_3': 0.0, 'bleu_4': 0.0}

def calculate_meteor_score_safe(reference, candidate):
    """Calcola METEOR score"""
    try:
        ref_tokens = reference.lower().split()
        cand_tokens = candidate.lower().split()
        
        if not ref_tokens or not cand_tokens:
            return 0.0
            
        return meteor_score([ref_tokens], cand_tokens)
        
    except Exception as e:
        logger.warning(f"Errore METEOR: {e}")
        return 0.0

def calculate_rouge_l(reference, candidate):
    """Calcola ROUGE-L score"""
    try:
        ref_tokens = reference.lower().split()
        cand_tokens = candidate.lower().split()
        
        if not ref_tokens or not cand_tokens:
            return 0.0
        
        # LCS (Longest Common Subsequence)
        def lcs_length(X, Y):
            m, n = len(X), len(Y)
            L = [[0] * (n + 1) for _ in range(m + 1)]
            
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if X[i-1] == Y[j-1]:
                        L[i][j] = L[i-1][j-1] + 1
                    else:
                        L[i][j] = max(L[i-1][j], L[i][j-1])
            
            return L[m][n]
        
        lcs_len = lcs_length(ref_tokens, cand_tokens)
        
        if len(ref_tokens) == 0 or len(cand_tokens) == 0:
            return 0.0
        
        precision = lcs_len / len(cand_tokens) if len(cand_tokens) > 0 else 0.0
        recall = lcs_len / len(ref_tokens) if len(ref_tokens) > 0 else 0.0
        
        if precision + recall == 0:
            return 0.0
        
        f1 = 2 * precision * recall / (precision + recall)
        return f1
        
    except Exception as e:
        logger.warning(f"Errore ROUGE-L: {e}")
        return 0.0

def calculate_blip1_metrics():
    """Calcola metriche per BLIP-1"""
    
    # File BLIP-1
    blip1_file = "evaluation_results/cpu_baseline_inference/blip1_minimal_20250730_154910.json"
    
    if not os.path.exists(blip1_file):
        logger.error(f"File BLIP-1 non trovato: {blip1_file}")
        return
    
    # Carica risultati
    with open(blip1_file, 'r') as f:
        results = json.load(f)
    
    logger.info(f"📊 Caricati {len(results)} risultati BLIP-1")
    
    # Calcola metriche
    bleu_1_scores = []
    bleu_2_scores = []
    bleu_3_scores = []
    bleu_4_scores = []
    meteor_scores = []
    rouge_l_scores = []
    
    successful = 0
    
    for i, result in enumerate(results):
        try:
            ground_truth = result.get('ground_truth', '')
            generated_caption = result.get('generated_caption', '')
            
            if not ground_truth or not generated_caption:
                continue
            
            # BLEU scores
            bleu_scores = calculate_bleu_scores(ground_truth, generated_caption)
            bleu_1_scores.append(bleu_scores['bleu_1'])
            bleu_2_scores.append(bleu_scores['bleu_2'])
            bleu_3_scores.append(bleu_scores['bleu_3'])
            bleu_4_scores.append(bleu_scores['bleu_4'])
            
            # METEOR
            meteor = calculate_meteor_score_safe(ground_truth, generated_caption)
            meteor_scores.append(meteor)
            
            # ROUGE-L
            rouge_l = calculate_rouge_l(ground_truth, generated_caption)
            rouge_l_scores.append(rouge_l)
            
            successful += 1
            
            if (i + 1) % 10 == 0:
                logger.info(f"📈 Progress: {i+1}/{len(results)}")
                
        except Exception as e:
            logger.warning(f"Errore esempio {i}: {e}")
            continue
    
    # Calcola statistiche
    metrics = {
        'model': 'BLIP-1',
        'timestamp': datetime.now().isoformat(),
        'total_examples': len(results),
        'successful_examples': successful,
        'method': 'real_calculated_from_results',
        'bleu_1': {
            'mean': np.mean(bleu_1_scores),
            'std': np.std(bleu_1_scores),
            'min': np.min(bleu_1_scores),
            'max': np.max(bleu_1_scores),
            'count': len(bleu_1_scores)
        },
        'bleu_2': {
            'mean': np.mean(bleu_2_scores),
            'std': np.std(bleu_2_scores),
            'min': np.min(bleu_2_scores),
            'max': np.max(bleu_2_scores),
            'count': len(bleu_2_scores)
        },
        'bleu_3': {
            'mean': np.mean(bleu_3_scores),
            'std': np.std(bleu_3_scores),
            'min': np.min(bleu_3_scores),
            'max': np.max(bleu_3_scores),
            'count': len(bleu_3_scores)
        },
        'bleu_4': {
            'mean': np.mean(bleu_4_scores),
            'std': np.std(bleu_4_scores),
            'min': np.min(bleu_4_scores),
            'max': np.max(bleu_4_scores),
            'count': len(bleu_4_scores)
        },
        'meteor': {
            'mean': np.mean(meteor_scores),
            'std': np.std(meteor_scores),
            'min': np.min(meteor_scores),
            'max': np.max(meteor_scores),
            'count': len(meteor_scores)
        },
        'rouge_l': {
            'mean': np.mean(rouge_l_scores),
            'std': np.std(rouge_l_scores),
            'min': np.min(rouge_l_scores),
            'max': np.max(rouge_l_scores),
            'count': len(rouge_l_scores)
        }
    }
    
    # Salva risultati
    output_dir = "evaluation_results/BLIP1_REAL_METRICS"
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"BLIP1_REAL_metrics_{timestamp}.json")
    
    with open(output_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Report
    logger.info("=" * 60)
    logger.info("✅ METRICHE BLIP-1 CALCOLATE!")
    logger.info("=" * 60)
    logger.info(f"📊 BLEU-1: {metrics['bleu_1']['mean']:.4f} ± {metrics['bleu_1']['std']:.4f}")
    logger.info(f"📊 BLEU-2: {metrics['bleu_2']['mean']:.4f} ± {metrics['bleu_2']['std']:.4f}")
    logger.info(f"📊 BLEU-3: {metrics['bleu_3']['mean']:.4f} ± {metrics['bleu_3']['std']:.4f}")
    logger.info(f"📊 BLEU-4: {metrics['bleu_4']['mean']:.4f} ± {metrics['bleu_4']['std']:.4f}")
    logger.info(f"📊 METEOR: {metrics['meteor']['mean']:.4f} ± {metrics['meteor']['std']:.4f}")
    logger.info(f"📊 ROUGE-L: {metrics['rouge_l']['mean']:.4f} ± {metrics['rouge_l']['std']:.4f}")
    logger.info(f"📊 Esempi: {successful}/{len(results)}")
    logger.info(f"📄 Salvato: {output_file}")
    logger.info("=" * 60)

if __name__ == "__main__":
    calculate_blip1_metrics()
