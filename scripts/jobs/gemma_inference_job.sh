#!/bin/bash
#SBATCH --job-name=gemma_inference
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=02:00:00
#SBATCH --output=logs/gemma_inference_%j.out
#SBATCH --error=logs/gemma_inference_%j.err

echo "🚀 Starting Gemma inference job..."
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Time: $(date)"

# Load environment
source activate svg_env_new

# Set working directory
cd /work/tesi_ediluzio

# Create logs directory
mkdir -p logs

# Run Gemma inference
echo "📥 Running Gemma inference..."
python scripts/inference/gemma_real_inference.py

echo "✅ Gemma inference job completed!"
echo "Time: $(date)"
