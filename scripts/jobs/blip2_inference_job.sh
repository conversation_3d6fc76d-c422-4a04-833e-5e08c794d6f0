#!/bin/bash
#SBATCH --job-name=blip2_inference
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=64G
#SBATCH --time=02:00:00
#SBATCH --output=logs/blip2_inference_%j.out
#SBATCH --error=logs/blip2_inference_%j.err

echo "🚀 Starting BLIP-2 inference job..."
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Time: $(date)"

# Load environment
source activate svg_env_new

# Set working directory
cd /work/tesi_ediluzio

# Create logs directory
mkdir -p logs

# Run BLIP-2 inference
echo "📥 Running BLIP-2 inference..."
python scripts/inference/blip2_minimal.py

echo "✅ BLIP-2 inference job completed!"
echo "Time: $(date)"
