#!/bin/bash

echo "🚀 RIAVVIO MODELLI QUANTIZZATI CON NOHUP"
echo "========================================"
echo "Time: $(date)"
echo ""

cd /work/tesi_ediluzio

# Function to start training with nohup
start_training_nohup() {
    local model_name=$1
    local model_path=$2
    local output_dir=$3
    local checkpoint_dir=$4
    local log_file=$5
    
    echo "🚀 Starting $model_name training..."
    echo "   Model: $model_path"
    echo "   Checkpoint: $checkpoint_dir"
    echo "   Log: $log_file"
    
    # Use nohup to run in background
    nohup python scripts/training/train_lora_multi_gpu_simple.py \
        --model_name_or_path "$model_path" \
        --data_file "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json" \
        --output_dir "$output_dir" \
        --resume_from_checkpoint "$checkpoint_dir" \
        --num_train_epochs 5 \
        --per_device_train_batch_size 1 \
        --gradient_accumulation_steps 16 \
        --learning_rate 2e-4 \
        --logging_steps 50 \
        --save_steps 500 \
        --max_seq_length 2048 \
        --use_wandb \
        --wandb_project "svg-captioning-quantized" \
        --wandb_run_name "${model_name}_resume_$(date +%Y%m%d_%H%M%S)" \
        > "$log_file" 2>&1 &
    
    local pid=$!
    echo "   ✅ Started with PID: $pid"
    echo "$pid" > "${output_dir}/training.pid"
    return 0
}

# Start Gemma-T9
echo "🔸 GEMMA-T9 QUANTIZZATO"
echo "------------------------"
GEMMA_OUTPUT="experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
GEMMA_CHECKPOINT="$GEMMA_OUTPUT/checkpoint-6000"
GEMMA_LOG="$GEMMA_OUTPUT/nohup_training.log"

if [ -d "$GEMMA_CHECKPOINT" ]; then
    start_training_nohup "gemma_t9" "google/gemma-2-9b-it" "$GEMMA_OUTPUT" "$GEMMA_CHECKPOINT" "$GEMMA_LOG"
    echo "   📝 Log: $GEMMA_LOG"
    echo "   📋 PID file: $GEMMA_OUTPUT/training.pid"
else
    echo "   ❌ Checkpoint not found: $GEMMA_CHECKPOINT"
fi

echo ""

# Start Llama-T8
echo "🔸 LLAMA-T8 QUANTIZZATO"
echo "------------------------"
LLAMA_OUTPUT="experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
LLAMA_CHECKPOINT="$LLAMA_OUTPUT/checkpoint-8000"
LLAMA_LOG="$LLAMA_OUTPUT/nohup_training.log"

if [ -d "$LLAMA_CHECKPOINT" ]; then
    start_training_nohup "llama_t8" "meta-llama/Llama-3.1-8B-Instruct" "$LLAMA_OUTPUT" "$LLAMA_CHECKPOINT" "$LLAMA_LOG"
    echo "   📝 Log: $LLAMA_LOG"
    echo "   📋 PID file: $LLAMA_OUTPUT/training.pid"
else
    echo "   ❌ Checkpoint not found: $LLAMA_CHECKPOINT"
fi

echo ""
echo "🎯 TRAINING STARTED IN BACKGROUND"
echo "=================================="

# Wait a moment for processes to start
sleep 5

# Check if processes are running
echo "📊 Process Status Check:"
if [ -f "$GEMMA_OUTPUT/training.pid" ]; then
    GEMMA_PID=$(cat "$GEMMA_OUTPUT/training.pid")
    if kill -0 $GEMMA_PID 2>/dev/null; then
        echo "   🟢 Gemma-T9 running (PID: $GEMMA_PID)"
    else
        echo "   🔴 Gemma-T9 not running"
    fi
fi

if [ -f "$LLAMA_OUTPUT/training.pid" ]; then
    LLAMA_PID=$(cat "$LLAMA_OUTPUT/training.pid")
    if kill -0 $LLAMA_PID 2>/dev/null; then
        echo "   🟢 Llama-T8 running (PID: $LLAMA_PID)"
    else
        echo "   🔴 Llama-T8 not running"
    fi
fi

echo ""
echo "💡 MONITORING COMMANDS:"
echo "   Check processes: python check_training_status.py"
echo "   View Gemma log: tail -f $GEMMA_LOG"
echo "   View Llama log: tail -f $LLAMA_LOG"
echo "   Kill Gemma: kill \$(cat $GEMMA_OUTPUT/training.pid)"
echo "   Kill Llama: kill \$(cat $LLAMA_OUTPUT/training.pid)"
echo ""
echo "✅ DONE - Training processes running in background with nohup"
