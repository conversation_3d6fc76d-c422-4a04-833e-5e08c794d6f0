#!/usr/bin/env python3
"""
📊 MONITOR TRAINING MODELLI QUANTIZZATI
Monitora lo stato del training dei modelli quantizzati
"""

import os
import time
import subprocess
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_process_status(pid):
    """Controlla se un processo è ancora in esecuzione"""
    try:
        result = subprocess.run(['ps', '-p', str(pid)], capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def get_gpu_usage():
    """Ottieni utilizzo GPU"""
    try:
        result = subprocess.run([
            'nvidia-smi', '--query-gpu=index,memory.used,memory.total,utilization.gpu', 
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            gpu_info = []
            for i, line in enumerate(lines):
                parts = line.split(', ')
                if len(parts) >= 4:
                    mem_used = int(parts[1])
                    mem_total = int(parts[2])
                    gpu_util = int(parts[3])
                    if mem_used > 100:  # Solo GPU con memoria utilizzata
                        gpu_info.append(f"GPU{i}: {mem_used}/{mem_total}MB ({gpu_util}%)")
            return gpu_info if gpu_info else ["Nessuna GPU in uso"]
        else:
            return ["Errore nvidia-smi"]
    except:
        return ["nvidia-smi non disponibile"]

def check_checkpoint_progress(model_dir):
    """Controlla i checkpoint più recenti"""
    try:
        checkpoints = []
        if os.path.exists(model_dir):
            for item in os.listdir(model_dir):
                if item.startswith('checkpoint-'):
                    try:
                        step = int(item.split('-')[1])
                        checkpoint_path = os.path.join(model_dir, item)
                        mtime = os.path.getmtime(checkpoint_path)
                        checkpoints.append((step, mtime, item))
                    except:
                        continue
        
        if checkpoints:
            # Ordina per step
            checkpoints.sort(key=lambda x: x[0])
            latest = checkpoints[-1]
            latest_time = datetime.fromtimestamp(latest[1]).strftime('%H:%M:%S')
            return f"Ultimo: {latest[2]} ({latest_time})"
        else:
            return "Nessun checkpoint"
    except:
        return "Errore lettura"

def main():
    """Main monitoring loop"""
    logger.info("📊 MONITOR TRAINING MODELLI QUANTIZZATI")
    logger.info("=" * 60)
    
    # PID dei processi (da aggiornare se necessario)
    gemma_pid = 2391928
    llama_pid = 2391929
    
    # Directory dei modelli
    gemma_dir = "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
    llama_dir = "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
    
    try:
        while True:
            logger.info(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - Status Check")
            logger.info("-" * 40)
            
            # Controlla processi
            gemma_running = check_process_status(gemma_pid)
            llama_running = check_process_status(llama_pid)
            
            logger.info(f"🔸 Gemma-T9 (PID {gemma_pid}): {'🟢 RUNNING' if gemma_running else '🔴 STOPPED'}")
            logger.info(f"🔸 Llama-T8 (PID {llama_pid}): {'🟢 RUNNING' if llama_running else '🔴 STOPPED'}")
            
            # Controlla checkpoint progress
            gemma_progress = check_checkpoint_progress(gemma_dir)
            llama_progress = check_checkpoint_progress(llama_dir)
            
            logger.info(f"📂 Gemma-T9 Progress: {gemma_progress}")
            logger.info(f"📂 Llama-T8 Progress: {llama_progress}")
            
            # Controlla GPU
            gpu_usage = get_gpu_usage()
            logger.info(f"🖥️  GPU Usage: {', '.join(gpu_usage)}")
            
            # Se entrambi i processi sono fermi, esci
            if not gemma_running and not llama_running:
                logger.info("⚠️ Entrambi i processi sono fermi!")
                break
            
            # Aspetta 30 secondi prima del prossimo check
            time.sleep(30)
            
    except KeyboardInterrupt:
        logger.info("\n🛑 Monitoring interrotto dall'utente")
    except Exception as e:
        logger.error(f"❌ Errore nel monitoring: {e}")

if __name__ == "__main__":
    main()
