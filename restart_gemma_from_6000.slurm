#!/bin/bash
#SBATCH --job-name=GEMMA_FROM_6000
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/GEMMA_FROM_6000_%j.out
#SBATCH --error=logs/GEMMA_FROM_6000_%j.err

echo "🚀 GEMMA FROM CHECKPOINT 6000 - $(date)"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \
    --model_name google/gemma-2-9b-it \
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json \
    --output_dir experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized \
    --resume_from_checkpoint experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/checkpoint-6000 \
    --use_wandb \
    --wandb_project svg-captioning-quantized \
    --wandb_run_name gemma_from_6000_$(date +%Y%m%d_%H%M%S)

echo "✅ GEMMA FROM 6000 completato - $(date)"
