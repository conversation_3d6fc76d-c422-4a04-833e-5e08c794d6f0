#!/usr/bin/env python3
"""
Script per convertire le immagini PNG in base64 e aggiornare l'HTML
"""

import base64
import os
import re
from pathlib import Path

def image_to_base64(image_path):
    """Converte un'immagine PNG in base64"""
    try:
        with open(image_path, 'rb') as img_file:
            img_data = img_file.read()
            base64_data = base64.b64encode(img_data).decode('utf-8')
            return f"data:image/png;base64,{base64_data}"
    except Exception as e:
        print(f"Errore nel convertire {image_path}: {e}")
        return None

def fix_html_images():
    """Aggiorna l'HTML con le immagini in base64"""
    
    # Path del file HTML
    html_file = "evaluation_results/HTML_ESEMPI_QUALITATIVI_FINALI_CORRETTI_20250731_163500.html"
    
    # Leggi il file HTML
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Lista delle immagini da convertire
    images_to_convert = [
        "data/processed/baseline_dataset_COMPLETE/images/baseline_0000.png",
        "data/processed/baseline_dataset_COMPLETE/images/baseline_0001.png", 
        "data/processed/baseline_dataset_COMPLETE/images/baseline_0002.png",
        "data/processed/baseline_dataset_COMPLETE/images/baseline_0003.png",
        "data/processed/baseline_dataset_COMPLETE/images/baseline_0004.png",
        # Idefics3 usa immagini diverse - uso quelle disponibili
        "data/processed/prerendered_svg/unknown_sr_000398.png",
        "data/processed/prerendered_svg/unknown_ki_0142151.png",
        "data/processed/prerendered_svg/unknown_ssl_105769.png",
        "data/processed/prerendered_svg/unknown_wd_110169.png"
    ]
    
    # Converti ogni immagine e sostituisci nell'HTML
    for img_path in images_to_convert:
        if os.path.exists(img_path):
            base64_data = image_to_base64(img_path)
            if base64_data:
                # Sostituisci il path relativo con base64
                old_src = f'src="../{img_path}"'
                new_src = f'src="{base64_data}"'
                html_content = html_content.replace(old_src, new_src)
                print(f"✅ Convertita: {img_path}")
            else:
                print(f"❌ Errore nella conversione: {img_path}")
        else:
            print(f"❌ File non trovato: {img_path}")
    
    # Salva il file HTML aggiornato
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"\n✅ File HTML aggiornato: {html_file}")
    print("🖼️ Tutte le immagini sono ora embedded in base64!")

if __name__ == "__main__":
    fix_html_images()
