#!/bin/bash

echo "🚀 RIAVVIO MODELLI QUANTIZZATI - AMBIENTE CORRETTO"
echo "=================================================="
echo "Time: $(date)"
echo ""

cd /work/tesi_ediluzio

# Activate the correct conda environment
echo "🔧 Attivazione ambiente svg_env_new..."
source /homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/etc/profile.d/conda.sh
conda activate svg_env_new

# Verify environment
echo "📋 Verifica ambiente:"
echo "   Python: $(which python)"
echo "   Transformers: $(python -c 'import transformers; print(transformers.__version__)')"
echo "   PyTorch: $(python -c 'import torch; print(torch.__version__)')"
echo ""

# Function to start training with correct environment
start_training_fixed() {
    local model_name=$1
    local model_path=$2
    local output_dir=$3
    local checkpoint_dir=$4
    local log_file=$5
    
    echo "🚀 Starting $model_name training..."
    echo "   Model: $model_path"
    echo "   Checkpoint: $checkpoint_dir"
    echo "   Log: $log_file"
    
    # Use nohup with correct conda environment
    nohup bash -c "
        source /homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/etc/profile.d/conda.sh
        conda activate svg_env_new
        python scripts/training/train_lora_multi_gpu_simple.py \
            --model_name_or_path '$model_path' \
            --data_file 'data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json' \
            --config_path 'configs/training/${model_name}_config.json' \
            --output_dir '$output_dir' \
            --resume_from_checkpoint '$checkpoint_dir' \
            --use_wandb \
            --wandb_project 'svg-captioning-quantized' \
            --wandb_run_name '${model_name}_resume_$(date +%Y%m%d_%H%M%S)'
    " > "$log_file" 2>&1 &
    
    local pid=$!
    echo "   ✅ Started with PID: $pid"
    echo "$pid" > "${output_dir}/training_FIXED.pid"
    return 0
}

# Start Gemma-T9
echo "🔸 GEMMA-T9 QUANTIZZATO"
echo "------------------------"
GEMMA_OUTPUT="experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
GEMMA_CHECKPOINT="$GEMMA_OUTPUT/checkpoint-6000"
GEMMA_LOG="$GEMMA_OUTPUT/FIXED_training.log"

if [ -d "$GEMMA_CHECKPOINT" ]; then
    start_training_fixed "gemma_t9" "google/gemma-2-9b-it" "$GEMMA_OUTPUT" "$GEMMA_CHECKPOINT" "$GEMMA_LOG"
    echo "   📝 Log: $GEMMA_LOG"
    echo "   📋 PID file: $GEMMA_OUTPUT/training_FIXED.pid"
else
    echo "   ❌ Checkpoint not found: $GEMMA_CHECKPOINT"
fi

echo ""

# Start Llama-T8
echo "🔸 LLAMA-T8 QUANTIZZATO"
echo "------------------------"
LLAMA_OUTPUT="experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
LLAMA_CHECKPOINT="$LLAMA_OUTPUT/checkpoint-8000"
LLAMA_LOG="$LLAMA_OUTPUT/FIXED_training.log"

if [ -d "$LLAMA_CHECKPOINT" ]; then
    start_training_fixed "llama_t8" "meta-llama/Llama-3.1-8B-Instruct" "$LLAMA_OUTPUT" "$LLAMA_CHECKPOINT" "$LLAMA_LOG"
    echo "   📝 Log: $LLAMA_LOG"
    echo "   📋 PID file: $LLAMA_OUTPUT/training_FIXED.pid"
else
    echo "   ❌ Checkpoint not found: $LLAMA_CHECKPOINT"
fi

echo ""
echo "🎯 TRAINING STARTED WITH FIXED ENVIRONMENT"
echo "==========================================="

# Wait a moment for processes to start
sleep 10

# Check if processes are running
echo "📊 Process Status Check:"
if [ -f "$GEMMA_OUTPUT/training_FIXED.pid" ]; then
    GEMMA_PID=$(cat "$GEMMA_OUTPUT/training_FIXED.pid")
    if kill -0 $GEMMA_PID 2>/dev/null; then
        echo "   🟢 Gemma-T9 running (PID: $GEMMA_PID)"
    else
        echo "   🔴 Gemma-T9 not running - check log: $GEMMA_LOG"
    fi
fi

if [ -f "$LLAMA_OUTPUT/training_FIXED.pid" ]; then
    LLAMA_PID=$(cat "$LLAMA_OUTPUT/training_FIXED.pid")
    if kill -0 $LLAMA_PID 2>/dev/null; then
        echo "   🟢 Llama-T8 running (PID: $LLAMA_PID)"
    else
        echo "   🔴 Llama-T8 not running - check log: $LLAMA_LOG"
    fi
fi

echo ""
echo "💡 MONITORING COMMANDS:"
echo "   Check processes: ps aux | grep train_lora"
echo "   View Gemma log: tail -f $GEMMA_LOG"
echo "   View Llama log: tail -f $LLAMA_LOG"
echo "   Kill Gemma: kill \$(cat $GEMMA_OUTPUT/training_FIXED.pid)"
echo "   Kill Llama: kill \$(cat $LLAMA_OUTPUT/training_FIXED.pid)"
echo ""
echo "✅ DONE - Training with FIXED environment (svg_env_new)"
