#!/usr/bin/env python3
"""
🚀 RESTART UN MODELLO ALLA VOLTA
Avvia un singolo modello quantizzato per evitare problemi di memoria
"""

import os
import sys
import subprocess
import logging
import argparse
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_checkpoint(model_dir):
    """Trova l'ultimo checkpoint in una directory"""
    if not os.path.exists(model_dir):
        return None
    
    checkpoints = []
    for item in os.listdir(model_dir):
        if item.startswith('checkpoint-'):
            try:
                step = int(item.split('-')[1])
                checkpoints.append((step, os.path.join(model_dir, item)))
            except:
                continue
    
    if not checkpoints:
        return None
    
    # Ordina per step e prendi l'ultimo
    checkpoints.sort(key=lambda x: x[0])
    return checkpoints[-1][1]

def restart_gemma():
    """Riavvia solo Gemma-T9"""
    logger.info("🚀 RIAVVIO GEMMA-T9 QUANTIZZATO")
    
    model_dir = "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
    config_path = "experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json"
    latest_checkpoint = find_latest_checkpoint(model_dir)
    
    if not latest_checkpoint:
        logger.error(f"❌ Nessun checkpoint trovato in {model_dir}")
        return False
    
    if not os.path.exists(config_path):
        logger.error(f"❌ Config non trovato: {config_path}")
        return False
    
    logger.info(f"📂 Ultimo checkpoint: {latest_checkpoint}")
    logger.info(f"⚙️ Config: {config_path}")
    
    # Comando con script semplice che funziona
    cmd = [
        "python", "scripts/training/train_lora_simple.py",
        "--model_name_or_path", "google/gemma-2-9b-it",
        "--data_file", "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json",
        "--config_path", config_path,
        "--output_dir", model_dir,
        "--resume_from_checkpoint", latest_checkpoint,
        "--use_wandb",
        "--wandb_project", "svg-captioning-quantized",
        "--wandb_run_name", f"gemma_t9_single_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    ]
    
    logger.info(f"🔧 Comando: {' '.join(cmd)}")
    
    try:
        # Avvia in foreground per vedere errori
        result = subprocess.run(cmd, cwd="/work/tesi_ediluzio")
        return result.returncode == 0
    except Exception as e:
        logger.error(f"❌ Errore: {e}")
        return False

def restart_llama():
    """Riavvia solo Llama-T8"""
    logger.info("🚀 RIAVVIO LLAMA-T8 QUANTIZZATO")
    
    model_dir = "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
    config_path = "experiments/xml_direct_input/configs/llama_t8_2gpu_final.json"
    latest_checkpoint = find_latest_checkpoint(model_dir)
    
    if not latest_checkpoint:
        logger.error(f"❌ Nessun checkpoint trovato in {model_dir}")
        return False
    
    if not os.path.exists(config_path):
        logger.error(f"❌ Config non trovato: {config_path}")
        return False
    
    logger.info(f"📂 Ultimo checkpoint: {latest_checkpoint}")
    logger.info(f"⚙️ Config: {config_path}")
    
    # Comando con script semplice che funziona
    cmd = [
        "python", "scripts/training/train_lora_simple.py",
        "--model_name_or_path", "meta-llama/Llama-3.1-8B-Instruct",
        "--data_file", "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json",
        "--config_path", config_path,
        "--output_dir", model_dir,
        "--resume_from_checkpoint", latest_checkpoint,
        "--use_wandb",
        "--wandb_project", "svg-captioning-quantized",
        "--wandb_run_name", f"llama_t8_single_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    ]
    
    logger.info(f"🔧 Comando: {' '.join(cmd)}")
    
    try:
        # Avvia in foreground per vedere errori
        result = subprocess.run(cmd, cwd="/work/tesi_ediluzio")
        return result.returncode == 0
    except Exception as e:
        logger.error(f"❌ Errore: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Riavvia un modello quantizzato')
    parser.add_argument('--model', choices=['gemma', 'llama'], required=True,
                       help='Modello da riavviare (gemma o llama)')
    args = parser.parse_args()
    
    logger.info("🚀 RIAVVIO MODELLO SINGOLO")
    logger.info("=" * 50)
    
    if args.model == 'gemma':
        success = restart_gemma()
    elif args.model == 'llama':
        success = restart_llama()
    
    if success:
        logger.info("✅ Modello avviato con successo!")
    else:
        logger.error("❌ Errore nell'avvio del modello")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
