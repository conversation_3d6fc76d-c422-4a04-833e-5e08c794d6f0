#!/usr/bin/env python3
"""
🚀 RESTART MODELLI QUANTIZZATI - VERSIONE SEMPLICE
Usa lo script multi-GPU che funzionava prima
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_checkpoint(model_dir):
    """Trova l'ultimo checkpoint in una directory"""
    if not os.path.exists(model_dir):
        return None
    
    checkpoints = []
    for item in os.listdir(model_dir):
        if item.startswith('checkpoint-'):
            try:
                step = int(item.split('-')[1])
                checkpoints.append((step, os.path.join(model_dir, item)))
            except:
                continue
    
    if not checkpoints:
        return None
    
    # Ordina per step e prendi l'ultimo
    checkpoints.sort(key=lambda x: x[0])
    return checkpoints[-1][1]

def restart_gemma_simple():
    """Riavvia Gemma-T9 con script semplice"""
    logger.info("🚀 RIAVVIO GEMMA-T9 QUANTIZZATO (SCRIPT SEMPLICE)")

    model_dir = "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
    config_path = "experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json"
    latest_checkpoint = find_latest_checkpoint(model_dir)

    if not latest_checkpoint:
        logger.error(f"❌ Nessun checkpoint trovato in {model_dir}")
        return False

    if not os.path.exists(config_path):
        logger.error(f"❌ Config non trovato: {config_path}")
        return False

    logger.info(f"📂 Ultimo checkpoint: {latest_checkpoint}")
    logger.info(f"⚙️ Config: {config_path}")

    # Comando con config path
    cmd = [
        "python", "scripts/training/train_lora_multi_gpu_simple.py",
        "--model_name_or_path", "google/gemma-2-9b-it",
        "--data_file", "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json",
        "--config_path", config_path,
        "--output_dir", model_dir,
        "--resume_from_checkpoint", latest_checkpoint,
        "--use_wandb",
        "--wandb_project", "svg-captioning-quantized",
        "--wandb_run_name", f"gemma_t9_resume_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    ]
    
    logger.info(f"🔧 Comando: {' '.join(cmd)}")
    
    try:
        # Comando con ambiente conda
        bash_cmd = f"""
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env
{' '.join(cmd)}
"""

        # Avvia in background
        with open(f"{model_dir}/training_output.log", "w") as log_file:
            process = subprocess.Popen(['bash', '-c', bash_cmd], stdout=log_file, stderr=subprocess.STDOUT)
        logger.info(f"✅ Gemma-T9 avviato (PID: {process.pid})")
        logger.info(f"📝 Log: {model_dir}/training_output.log")
        return True
    except Exception as e:
        logger.error(f"❌ Errore: {e}")
        return False

def restart_llama_simple():
    """Riavvia Llama-T8 con script semplice"""
    logger.info("🚀 RIAVVIO LLAMA-T8 QUANTIZZATO (SCRIPT SEMPLICE)")

    model_dir = "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
    config_path = "experiments/xml_direct_input/configs/llama_t8_2gpu_final.json"
    latest_checkpoint = find_latest_checkpoint(model_dir)

    if not latest_checkpoint:
        logger.error(f"❌ Nessun checkpoint trovato in {model_dir}")
        return False

    if not os.path.exists(config_path):
        logger.error(f"❌ Config non trovato: {config_path}")
        return False

    logger.info(f"📂 Ultimo checkpoint: {latest_checkpoint}")
    logger.info(f"⚙️ Config: {config_path}")

    # Comando con config path
    cmd = [
        "python", "scripts/training/train_lora_multi_gpu_simple.py",
        "--model_name_or_path", "meta-llama/Llama-3.1-8B-Instruct",
        "--data_file", "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json",
        "--config_path", config_path,
        "--output_dir", model_dir,
        "--resume_from_checkpoint", latest_checkpoint,
        "--use_wandb",
        "--wandb_project", "svg-captioning-quantized",
        "--wandb_run_name", f"llama_t8_resume_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    ]
    
    logger.info(f"🔧 Comando: {' '.join(cmd)}")
    
    try:
        # Comando con ambiente conda
        bash_cmd = f"""
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env
{' '.join(cmd)}
"""

        # Avvia in background
        with open(f"{model_dir}/training_output.log", "w") as log_file:
            process = subprocess.Popen(['bash', '-c', bash_cmd], stdout=log_file, stderr=subprocess.STDOUT)
        logger.info(f"✅ Llama-T8 avviato (PID: {process.pid})")
        logger.info(f"📝 Log: {model_dir}/training_output.log")
        return True
    except Exception as e:
        logger.error(f"❌ Errore: {e}")
        return False

def main():
    """Main function"""
    logger.info("🚀 RIAVVIO MODELLI QUANTIZZATI (VERSIONE SEMPLICE)")
    logger.info("=" * 60)
    
    # Riavvia entrambi i modelli
    results = []
    
    # 1. Gemma-T9
    logger.info("\n" + "="*40)
    success_gemma = restart_gemma_simple()
    results.append(("Gemma-T9", success_gemma))
    
    # 2. Llama-T8
    logger.info("\n" + "="*40)
    success_llama = restart_llama_simple()
    results.append(("Llama-T8", success_llama))
    
    # Riepilogo
    logger.info("\n" + "="*60)
    logger.info("📊 RIEPILOGO:")
    for model_name, success in results:
        status = "✅ AVVIATO" if success else "❌ FALLITO"
        logger.info(f"  {model_name}: {status}")
    
    successful = sum(1 for _, success in results if success)
    logger.info(f"\n🎯 Modelli avviati: {successful}/2")
    
    if successful > 0:
        logger.info("💡 Controlla i log:")
        logger.info("   - experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/training_output.log")
        logger.info("   - experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/training_output.log")
        logger.info("💡 Usa 'python check_training_status.py' per monitorare")
    
    return successful == 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
