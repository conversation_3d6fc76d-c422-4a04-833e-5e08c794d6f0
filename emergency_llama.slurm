#!/bin/bash
#SBATCH --job-name=EMERGENCY_LLAMA
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/EMERGENCY_LLAMA_%j.out
#SBATCH --error=logs/EMERGENCY_LLAMA_%j.err

echo "🚨 EMERGENCY LLAMA - $(date)"

cd /work/tesi_ediluzio

# Configura token Hugging Face
export HF_TOKEN=*************************************
export HUGGING_FACE_HUB_TOKEN=*************************************

# Usa lo script ULTRA_QUANTIZED che funzionava
/homes/ediluzio/.conda/envs/clip_env/bin/python scripts/training/train_lora_ULTRA_QUANTIZED.py \
    --model_name meta-llama/Llama-2-7b-chat-hf \
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t8_scratch_quantized \
    --resume_from_checkpoint experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/checkpoint-8000 \
    --use_wandb \
    --wandb_project svg-captioning-quantized \
    --wandb_run_name emergency_llama_$(date +%Y%m%d_%H%M%S)

echo "✅ EMERGENCY LLAMA completato - $(date)"
