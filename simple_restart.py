#!/usr/bin/env python3
"""
🚀 RESTART SEMPLICE SENZA PEFT
Usa solo script che funzionavano prima
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_checkpoint(model_dir):
    """Trova l'ultimo checkpoint in una directory"""
    if not os.path.exists(model_dir):
        return None
    
    checkpoints = []
    for item in os.listdir(model_dir):
        if item.startswith('checkpoint-'):
            try:
                step = int(item.split('-')[1])
                checkpoints.append((step, os.path.join(model_dir, item)))
            except:
                continue
    
    if not checkpoints:
        return None
    
    # Ordina per step e prendi l'ultimo
    checkpoints.sort(key=lambda x: x[0])
    return checkpoints[-1][1]

def restart_with_slurm():
    """Riavvia con SLURM che funzionava"""
    logger.info("🚀 RIAVVIO CON SLURM")
    
    # Trova checkpoints
    gemma_dir = "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
    llama_dir = "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
    
    gemma_checkpoint = find_latest_checkpoint(gemma_dir)
    llama_checkpoint = find_latest_checkpoint(llama_dir)
    
    logger.info(f"📂 Gemma checkpoint: {gemma_checkpoint}")
    logger.info(f"📂 Llama checkpoint: {llama_checkpoint}")
    
    # Crea script SLURM temporaneo per Gemma
    if gemma_checkpoint:
        slurm_script = f"""#!/bin/bash
#SBATCH --job-name=GEMMA_RESTART
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/GEMMA_RESTART_%j.out
#SBATCH --error=logs/GEMMA_RESTART_%j.err

echo "🚀 GEMMA RESTART - $(date)"
echo "Checkpoint: {gemma_checkpoint}"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name google/gemma-2-9b-it \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json \\
    --output_dir {gemma_dir} \\
    --resume_from_checkpoint {gemma_checkpoint} \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name gemma_restart_$(date +%Y%m%d_%H%M%S)

echo "✅ GEMMA RESTART completato - $(date)"
"""
        
        with open("restart_gemma.slurm", "w") as f:
            f.write(slurm_script)
        
        # Lancia job
        result = subprocess.run(["sbatch", "restart_gemma.slurm"], capture_output=True, text=True)
        if result.returncode == 0:
            job_id = result.stdout.strip().split()[-1]
            logger.info(f"✅ Gemma job lanciato: {job_id}")
        else:
            logger.error(f"❌ Errore Gemma: {result.stderr}")
    
    # Crea script SLURM temporaneo per Llama
    if llama_checkpoint:
        slurm_script = f"""#!/bin/bash
#SBATCH --job-name=LLAMA_RESTART
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --output=logs/LLAMA_RESTART_%j.out
#SBATCH --error=logs/LLAMA_RESTART_%j.err

echo "🚀 LLAMA RESTART - $(date)"
echo "Checkpoint: {llama_checkpoint}"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

python scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name meta-llama/Llama-3.1-8B-Instruct \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \\
    --output_dir {llama_dir} \\
    --resume_from_checkpoint {llama_checkpoint} \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name llama_restart_$(date +%Y%m%d_%H%M%S)

echo "✅ LLAMA RESTART completato - $(date)"
"""
        
        with open("restart_llama.slurm", "w") as f:
            f.write(slurm_script)
        
        # Lancia job
        result = subprocess.run(["sbatch", "restart_llama.slurm"], capture_output=True, text=True)
        if result.returncode == 0:
            job_id = result.stdout.strip().split()[-1]
            logger.info(f"✅ Llama job lanciato: {job_id}")
        else:
            logger.error(f"❌ Errore Llama: {result.stderr}")

def main():
    logger.info("🚀 RESTART SEMPLICE CON SLURM")
    logger.info("=" * 50)
    
    restart_with_slurm()
    
    logger.info("💡 Controlla i job con: squeue -u ediluzio")
    logger.info("💡 Controlla i log in: logs/")

if __name__ == "__main__":
    main()
