#!/usr/bin/env python3
"""
🚀 RESTART MODELLI QUANTIZZATI DALL'ULTIMO CHECKPOINT
Riavvia il training dei 2 modelli quantizzati dall'ultimo salvataggio
"""

import os
import sys
import subprocess
import logging
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_checkpoint(model_dir):
    """Trova l'ultimo checkpoint in una directory"""
    if not os.path.exists(model_dir):
        return None
    
    checkpoints = []
    for item in os.listdir(model_dir):
        if item.startswith('checkpoint-'):
            try:
                step = int(item.split('-')[1])
                checkpoints.append((step, os.path.join(model_dir, item)))
            except:
                continue
    
    if not checkpoints:
        return None
    
    # Ordina per step e prendi l'ultimo
    checkpoints.sort(key=lambda x: x[0])
    return checkpoints[-1][1]

def restart_gemma_quantized():
    """Riavvia Gemma-T9 quantizzato dall'ultimo checkpoint"""
    logger.info("🚀 RIAVVIO GEMMA-T9 QUANTIZZATO")
    
    model_dir = "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
    latest_checkpoint = find_latest_checkpoint(model_dir)
    
    if not latest_checkpoint:
        logger.error(f"❌ Nessun checkpoint trovato in {model_dir}")
        return False
    
    logger.info(f"📂 Ultimo checkpoint trovato: {latest_checkpoint}")
    
    # Comando per riavviare il training
    cmd = [
        "python", "scripts/training/train_lora_ULTRA_QUANTIZED.py",
        "--model_name", "google/gemma-2-9b-it",
        "--data_file", "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json",
        "--config_path", "configs/training/gemma_t9_config.json",
        "--output_dir", model_dir,
        "--resume_from_checkpoint", latest_checkpoint,
        "--use_wandb",
        "--wandb_project", "svg-captioning-quantized",
        "--wandb_run_name", f"gemma_t9_quantized_resume_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    ]
    
    logger.info(f"🔧 Comando: {' '.join(cmd)}")
    
    try:
        # Avvia il processo in background
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        logger.info(f"✅ Gemma-T9 quantizzato avviato (PID: {process.pid})")
        return True
    except Exception as e:
        logger.error(f"❌ Errore nell'avvio Gemma-T9: {e}")
        return False

def restart_llama_quantized():
    """Riavvia Llama-T8 quantizzato dall'ultimo checkpoint"""
    logger.info("🚀 RIAVVIO LLAMA-T8 QUANTIZZATO")
    
    model_dir = "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
    latest_checkpoint = find_latest_checkpoint(model_dir)
    
    if not latest_checkpoint:
        logger.error(f"❌ Nessun checkpoint trovato in {model_dir}")
        return False
    
    logger.info(f"📂 Ultimo checkpoint trovato: {latest_checkpoint}")
    
    # Comando per riavviare il training
    cmd = [
        "python", "scripts/training/train_lora_ULTRA_QUANTIZED.py",
        "--model_name", "meta-llama/Llama-3.1-8B-Instruct",
        "--data_file", "data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json",
        "--config_path", "configs/training/llama_t8_config.json",
        "--output_dir", model_dir,
        "--resume_from_checkpoint", latest_checkpoint,
        "--use_wandb",
        "--wandb_project", "svg-captioning-quantized",
        "--wandb_run_name", f"llama_t8_quantized_resume_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    ]
    
    logger.info(f"🔧 Comando: {' '.join(cmd)}")
    
    try:
        # Avvia il processo in background
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        logger.info(f"✅ Llama-T8 quantizzato avviato (PID: {process.pid})")
        return True
    except Exception as e:
        logger.error(f"❌ Errore nell'avvio Llama-T8: {e}")
        return False

def main():
    """Main function"""
    logger.info("🚀 RIAVVIO MODELLI QUANTIZZATI DALL'ULTIMO CHECKPOINT")
    logger.info("=" * 60)
    
    # Controlla che i file di config esistano
    config_files = [
        "configs/training/gemma_t9_config.json",
        "configs/training/llama_t8_config.json"
    ]
    
    for config_file in config_files:
        if not os.path.exists(config_file):
            logger.warning(f"⚠️ File config non trovato: {config_file}")
    
    # Riavvia entrambi i modelli
    results = []
    
    # 1. Gemma-T9 quantizzato
    logger.info("\n" + "="*40)
    success_gemma = restart_gemma_quantized()
    results.append(("Gemma-T9 Quantizzato", success_gemma))
    
    # 2. Llama-T8 quantizzato  
    logger.info("\n" + "="*40)
    success_llama = restart_llama_quantized()
    results.append(("Llama-T8 Quantizzato", success_llama))
    
    # Riepilogo
    logger.info("\n" + "="*60)
    logger.info("📊 RIEPILOGO RIAVVII:")
    for model_name, success in results:
        status = "✅ AVVIATO" if success else "❌ FALLITO"
        logger.info(f"  {model_name}: {status}")
    
    successful_restarts = sum(1 for _, success in results if success)
    logger.info(f"\n🎯 Modelli riavviati con successo: {successful_restarts}/2")
    
    if successful_restarts > 0:
        logger.info("💡 Usa 'nvidia-smi' per monitorare l'utilizzo GPU")
        logger.info("💡 Usa 'ps aux | grep python' per vedere i processi attivi")
    
    return successful_restarts == 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
