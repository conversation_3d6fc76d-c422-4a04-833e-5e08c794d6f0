#!/usr/bin/env python3
"""
🚀 RESTART MODELLI QUANTIZZATI - MULTI GPU
Usa SLURM per training multi-GPU su 2 GPU
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_checkpoint(model_dir):
    """Trova l'ultimo checkpoint in una directory"""
    if not os.path.exists(model_dir):
        return None
    
    checkpoints = []
    for item in os.listdir(model_dir):
        if item.startswith('checkpoint-'):
            try:
                step = int(item.split('-')[1])
                checkpoints.append((step, os.path.join(model_dir, item)))
            except:
                continue
    
    if not checkpoints:
        return None
    
    # Ordina per step e prendi l'ultimo
    checkpoints.sort(key=lambda x: x[0])
    return checkpoints[-1][1]

def restart_multi_gpu():
    """Riavvia con SLURM multi-GPU"""
    logger.info("🚀 RIAVVIO MULTI-GPU CON SLURM")
    
    # Trova checkpoints
    gemma_dir = "experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
    llama_dir = "experiments/xml_direct_input/outputs/llama_t8_scratch_quantized"
    
    gemma_checkpoint = find_latest_checkpoint(gemma_dir)
    llama_checkpoint = find_latest_checkpoint(llama_dir)
    
    logger.info(f"📂 Gemma checkpoint: {gemma_checkpoint}")
    logger.info(f"📂 Llama checkpoint: {llama_checkpoint}")
    
    # Crea script SLURM per Gemma multi-GPU
    if gemma_checkpoint:
        slurm_script = f"""#!/bin/bash
#SBATCH --job-name=GEMMA_MULTI_GPU
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --output=logs/GEMMA_MULTI_GPU_%j.out
#SBATCH --error=logs/GEMMA_MULTI_GPU_%j.err

echo "🚀 GEMMA MULTI-GPU - $(date)"
echo "Checkpoint: {gemma_checkpoint}"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

# Multi-GPU training con torchrun
torchrun --nproc_per_node=2 --master_port=29500 \\
    scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name google/gemma-2-9b-it \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/gemma_t9_2gpu_final.json \\
    --output_dir {gemma_dir} \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name gemma_multi_gpu_$(date +%Y%m%d_%H%M%S)

echo "✅ GEMMA MULTI-GPU completato - $(date)"
"""
        
        with open("restart_gemma_multi_gpu.slurm", "w") as f:
            f.write(slurm_script)
        
        # Lancia job
        result = subprocess.run(["sbatch", "restart_gemma_multi_gpu.slurm"], capture_output=True, text=True)
        if result.returncode == 0:
            job_id = result.stdout.strip().split()[-1]
            logger.info(f"✅ Gemma multi-GPU job lanciato: {job_id}")
        else:
            logger.error(f"❌ Errore Gemma: {result.stderr}")
    
    # Crea script SLURM per Llama multi-GPU
    if llama_checkpoint:
        slurm_script = f"""#!/bin/bash
#SBATCH --job-name=LLAMA_MULTI_GPU
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --output=logs/LLAMA_MULTI_GPU_%j.out
#SBATCH --error=logs/LLAMA_MULTI_GPU_%j.err

echo "🚀 LLAMA MULTI-GPU - $(date)"
echo "Checkpoint: {llama_checkpoint}"

cd /work/tesi_ediluzio
source /homes/ediluzio/.conda/etc/profile.d/conda.sh
conda activate clip_env

# Multi-GPU training con torchrun
torchrun --nproc_per_node=2 --master_port=29501 \\
    scripts/training/train_lora_ULTRA_QUANTIZED.py \\
    --model_name meta-llama/Llama-3.1-8B-Instruct \\
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \\
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \\
    --output_dir {llama_dir} \\
    --use_wandb \\
    --wandb_project svg-captioning-quantized \\
    --wandb_run_name llama_multi_gpu_$(date +%Y%m%d_%H%M%S)

echo "✅ LLAMA MULTI-GPU completato - $(date)"
"""
        
        with open("restart_llama_multi_gpu.slurm", "w") as f:
            f.write(slurm_script)
        
        # Lancia job
        result = subprocess.run(["sbatch", "restart_llama_multi_gpu.slurm"], capture_output=True, text=True)
        if result.returncode == 0:
            job_id = result.stdout.strip().split()[-1]
            logger.info(f"✅ Llama multi-GPU job lanciato: {job_id}")
        else:
            logger.error(f"❌ Errore Llama: {result.stderr}")

def main():
    logger.info("🚀 RESTART MULTI-GPU CON SLURM")
    logger.info("=" * 50)
    
    restart_multi_gpu()
    
    logger.info("💡 Controlla i job con: squeue -u ediluzio")
    logger.info("💡 Controlla i log in: logs/")
    logger.info("💡 Multi-GPU richiede 2 GPU per job")

if __name__ == "__main__":
    main()
